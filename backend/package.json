{"name": "harvest-profit-pro-backend", "version": "1.0.0", "description": "Backend API for Harvest Profit Pro - Farm Finance Management System", "type": "module", "main": "src/server.js", "scripts": {"dev": "nodemon src/server.js", "start": "node src/server.js", "build": "echo 'No build step required for Node.js'", "test": "jest --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test:watch": "jest --watch --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test:coverage": "jest --coverage --detect<PERSON><PERSON><PERSON><PERSON><PERSON>", "test:integration": "jest --testPathPattern=integration --detectO<PERSON>Handles", "lint": "eslint src/ --ext .js", "lint:fix": "eslint src/ --ext .js --fix", "db:migrate": "npx sequelize-cli db:migrate", "db:migrate:undo": "npx sequelize-cli db:migrate:undo", "db:seed": "npx sequelize-cli db:seed:all", "db:seed:undo": "npx sequelize-cli db:seed:undo:all", "db:reset": "npm run db:migrate:undo:all && npm run db:migrate && npm run db:seed", "db:create": "npx sequelize-cli db:create", "db:drop": "npx sequelize-cli db:drop"}, "dependencies": {"express": "^4.18.2", "sequelize": "^6.35.0", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "bcrypt": "^5.1.1", "jsonwebtoken": "^9.0.2", "helmet": "^7.1.0", "cors": "^2.8.5", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "joi": "^17.11.0", "dotenv": "^16.3.1", "redis": "^4.6.10", "socket.io": "^4.7.4", "multer": "^1.4.5-lts.1", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "eslint-config-node": "^4.1.0", "sequelize-cli": "^6.6.2", "@types/jest": "^29.5.8"}, "keywords": ["farm", "finance", "agriculture", "api", "backend", "nodejs", "express", "postgresql"], "author": "Harvest Profit Pro Team", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}