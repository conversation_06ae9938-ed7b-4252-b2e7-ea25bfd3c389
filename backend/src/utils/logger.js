import winston from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';
import config from '../config/index.js';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Winston Logger Configuration
 * Provides structured logging with multiple transports and log rotation
 */

// Define log levels and colors
const logLevels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4
};

const logColors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'blue'
};

winston.addColors(logColors);

// Custom log format
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let log = `${timestamp} [${level.toUpperCase()}]: ${message}`;
    
    if (stack) {
      log += `\nStack: ${stack}`;
    }
    
    if (Object.keys(meta).length > 0) {
      log += `\nMeta: ${JSON.stringify(meta, null, 2)}`;
    }
    
    return log;
  })
);

// Console format for development
const consoleFormat = winston.format.combine(
  winston.format.colorize({ all: true }),
  winston.format.timestamp({ format: 'HH:mm:ss' }),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let log = `${timestamp} ${level}: ${message}`;
    
    if (stack) {
      log += `\n${stack}`;
    }
    
    if (Object.keys(meta).length > 0) {
      log += `\n${JSON.stringify(meta, null, 2)}`;
    }
    
    return log;
  })
);

// Create logs directory
const logsDir = path.join(process.cwd(), 'logs');

// Daily rotate file transport for all logs
const allLogsTransport = new DailyRotateFile({
  filename: path.join(logsDir, 'all-%DATE%.log'),
  datePattern: 'YYYY-MM-DD',
  maxSize: config.LOG_FILE_MAX_SIZE,
  maxFiles: config.LOG_FILE_MAX_FILES,
  format: logFormat,
  level: 'debug'
});

// Daily rotate file transport for error logs
const errorLogsTransport = new DailyRotateFile({
  filename: path.join(logsDir, 'error-%DATE%.log'),
  datePattern: 'YYYY-MM-DD',
  maxSize: config.LOG_FILE_MAX_SIZE,
  maxFiles: config.LOG_FILE_MAX_FILES,
  format: logFormat,
  level: 'error'
});

// Daily rotate file transport for HTTP logs
const httpLogsTransport = new DailyRotateFile({
  filename: path.join(logsDir, 'http-%DATE%.log'),
  datePattern: 'YYYY-MM-DD',
  maxSize: config.LOG_FILE_MAX_SIZE,
  maxFiles: config.LOG_FILE_MAX_FILES,
  format: logFormat,
  level: 'http'
});

// Security logs transport
const securityLogsTransport = new DailyRotateFile({
  filename: path.join(logsDir, 'security-%DATE%.log'),
  datePattern: 'YYYY-MM-DD',
  maxSize: config.LOG_FILE_MAX_SIZE,
  maxFiles: config.LOG_FILE_MAX_FILES,
  format: logFormat,
  level: 'warn'
});

// Performance logs transport
const performanceLogsTransport = new DailyRotateFile({
  filename: path.join(logsDir, 'performance-%DATE%.log'),
  datePattern: 'YYYY-MM-DD',
  maxSize: config.LOG_FILE_MAX_SIZE,
  maxFiles: config.LOG_FILE_MAX_FILES,
  format: logFormat,
  level: 'info'
});

// Create the main logger
const logger = winston.createLogger({
  level: config.LOG_LEVEL,
  levels: logLevels,
  format: logFormat,
  defaultMeta: {
    service: 'harvest-profit-pro-api',
    environment: config.NODE_ENV,
    version: process.env.npm_package_version || '1.0.0'
  },
  transports: [
    allLogsTransport,
    errorLogsTransport,
    httpLogsTransport
  ],
  exitOnError: false
});

// Add console transport for development
if (config.NODE_ENV === 'development') {
  logger.add(new winston.transports.Console({
    format: consoleFormat,
    level: 'debug'
  }));
}

// Create specialized loggers
const securityLogger = winston.createLogger({
  level: 'warn',
  levels: logLevels,
  format: logFormat,
  defaultMeta: {
    service: 'harvest-profit-pro-security',
    environment: config.NODE_ENV
  },
  transports: [securityLogsTransport],
  exitOnError: false
});

const performanceLogger = winston.createLogger({
  level: 'info',
  levels: logLevels,
  format: logFormat,
  defaultMeta: {
    service: 'harvest-profit-pro-performance',
    environment: config.NODE_ENV
  },
  transports: [performanceLogsTransport],
  exitOnError: false
});

// Logger utility functions
const loggerUtils = {
  /**
   * Log HTTP request
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {number} responseTime - Response time in milliseconds
   */
  logHttpRequest: (req, res, responseTime) => {
    const logData = {
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      responseTime: `${responseTime}ms`,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.id,
      requestId: req.id
    };

    if (res.statusCode >= 400) {
      logger.warn('HTTP Request Error', logData);
    } else {
      logger.http('HTTP Request', logData);
    }
  },

  /**
   * Log security event
   * @param {string} event - Security event type
   * @param {Object} details - Event details
   * @param {Object} req - Express request object
   */
  logSecurityEvent: (event, details, req = null) => {
    const logData = {
      event,
      details,
      timestamp: new Date().toISOString()
    };

    if (req) {
      logData.ip = req.ip;
      logData.userAgent = req.get('User-Agent');
      logData.url = req.originalUrl;
      logData.method = req.method;
      logData.userId = req.user?.id;
    }

    securityLogger.warn('Security Event', logData);
  },

  /**
   * Log performance metric
   * @param {string} metric - Metric name
   * @param {number} value - Metric value
   * @param {string} unit - Metric unit
   * @param {Object} context - Additional context
   */
  logPerformance: (metric, value, unit = 'ms', context = {}) => {
    performanceLogger.info('Performance Metric', {
      metric,
      value,
      unit,
      context,
      timestamp: new Date().toISOString()
    });
  },

  /**
   * Log database query performance
   * @param {string} query - SQL query
   * @param {number} duration - Query duration in milliseconds
   * @param {Object} params - Query parameters
   */
  logDatabaseQuery: (query, duration, params = {}) => {
    const logData = {
      query: query.substring(0, 200), // Truncate long queries
      duration: `${duration}ms`,
      params: Object.keys(params).length > 0 ? params : undefined,
      timestamp: new Date().toISOString()
    };

    if (duration > 1000) { // Log slow queries
      logger.warn('Slow Database Query', logData);
    } else {
      logger.debug('Database Query', logData);
    }
  },

  /**
   * Log authentication event
   * @param {string} event - Auth event type
   * @param {string} userId - User ID
   * @param {Object} details - Event details
   * @param {Object} req - Express request object
   */
  logAuthEvent: (event, userId, details = {}, req = null) => {
    const logData = {
      event,
      userId,
      details,
      timestamp: new Date().toISOString()
    };

    if (req) {
      logData.ip = req.ip;
      logData.userAgent = req.get('User-Agent');
    }

    logger.info('Authentication Event', logData);
  },

  /**
   * Log application error
   * @param {Error} error - Error object
   * @param {Object} context - Error context
   */
  logError: (error, context = {}) => {
    logger.error('Application Error', {
      message: error.message,
      stack: error.stack,
      name: error.name,
      context,
      timestamp: new Date().toISOString()
    });
  },

  /**
   * Log business logic event
   * @param {string} event - Business event type
   * @param {Object} data - Event data
   */
  logBusinessEvent: (event, data = {}) => {
    logger.info('Business Event', {
      event,
      data,
      timestamp: new Date().toISOString()
    });
  }
};

// Handle logging errors
logger.on('error', (error) => {
  console.error('Logger error:', error);
});

allLogsTransport.on('error', (error) => {
  console.error('All logs transport error:', error);
});

errorLogsTransport.on('error', (error) => {
  console.error('Error logs transport error:', error);
});

// Export logger and utilities
export default logger;
export { securityLogger, performanceLogger, loggerUtils };
