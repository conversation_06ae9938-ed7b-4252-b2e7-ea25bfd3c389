/**
 * Custom Error Classes
 * Provides structured error handling with specific error types
 */

/**
 * Base application error class
 */
export class AppError extends Error {
  constructor(message, statusCode = 500, code = 'INTERNAL_ERROR', isOperational = true) {
    super(message);
    
    this.name = this.constructor.name;
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = isOperational;
    this.timestamp = new Date().toISOString();
    
    Error.captureStackTrace(this, this.constructor);
  }

  toJSON() {
    return {
      name: this.name,
      message: this.message,
      statusCode: this.statusCode,
      code: this.code,
      timestamp: this.timestamp,
      stack: this.stack
    };
  }
}

/**
 * Validation error (400)
 */
export class ValidationError extends AppError {
  constructor(message, errors = [], field = null) {
    super(message, 400, 'VALIDATION_ERROR');
    this.errors = errors;
    this.field = field;
  }

  toJSON() {
    return {
      ...super.toJSON(),
      errors: this.errors,
      field: this.field
    };
  }
}

/**
 * Authentication error (401)
 */
export class AuthenticationError extends AppError {
  constructor(message = 'Authentication failed', code = 'AUTH_FAILED') {
    super(message, 401, code);
  }
}

/**
 * Authorization error (403)
 */
export class AuthorizationError extends AppError {
  constructor(message = 'Access forbidden', requiredRole = null) {
    super(message, 403, 'ACCESS_FORBIDDEN');
    this.requiredRole = requiredRole;
  }

  toJSON() {
    return {
      ...super.toJSON(),
      requiredRole: this.requiredRole
    };
  }
}

/**
 * Not found error (404)
 */
export class NotFoundError extends AppError {
  constructor(message = 'Resource not found', resource = null) {
    super(message, 404, 'NOT_FOUND');
    this.resource = resource;
  }

  toJSON() {
    return {
      ...super.toJSON(),
      resource: this.resource
    };
  }
}

/**
 * Conflict error (409)
 */
export class ConflictError extends AppError {
  constructor(message = 'Resource conflict', conflictingField = null) {
    super(message, 409, 'CONFLICT');
    this.conflictingField = conflictingField;
  }

  toJSON() {
    return {
      ...super.toJSON(),
      conflictingField: this.conflictingField
    };
  }
}

/**
 * Rate limit error (429)
 */
export class RateLimitError extends AppError {
  constructor(message = 'Too many requests', retryAfter = null) {
    super(message, 429, 'RATE_LIMIT_EXCEEDED');
    this.retryAfter = retryAfter;
  }

  toJSON() {
    return {
      ...super.toJSON(),
      retryAfter: this.retryAfter
    };
  }
}

/**
 * Database error (500)
 */
export class DatabaseError extends AppError {
  constructor(message = 'Database operation failed', originalError = null) {
    super(message, 500, 'DATABASE_ERROR');
    this.originalError = originalError;
  }

  toJSON() {
    return {
      ...super.toJSON(),
      originalError: this.originalError ? {
        name: this.originalError.name,
        message: this.originalError.message
      } : null
    };
  }
}

/**
 * External service error (502)
 */
export class ExternalServiceError extends AppError {
  constructor(message = 'External service error', service = null, originalError = null) {
    super(message, 502, 'EXTERNAL_SERVICE_ERROR');
    this.service = service;
    this.originalError = originalError;
  }

  toJSON() {
    return {
      ...super.toJSON(),
      service: this.service,
      originalError: this.originalError ? {
        name: this.originalError.name,
        message: this.originalError.message
      } : null
    };
  }
}

/**
 * Business logic error (422)
 */
export class BusinessLogicError extends AppError {
  constructor(message, businessRule = null) {
    super(message, 422, 'BUSINESS_LOGIC_ERROR');
    this.businessRule = businessRule;
  }

  toJSON() {
    return {
      ...super.toJSON(),
      businessRule: this.businessRule
    };
  }
}

/**
 * File upload error (400)
 */
export class FileUploadError extends AppError {
  constructor(message = 'File upload failed', fileType = null, maxSize = null) {
    super(message, 400, 'FILE_UPLOAD_ERROR');
    this.fileType = fileType;
    this.maxSize = maxSize;
  }

  toJSON() {
    return {
      ...super.toJSON(),
      fileType: this.fileType,
      maxSize: this.maxSize
    };
  }
}

/**
 * Configuration error (500)
 */
export class ConfigurationError extends AppError {
  constructor(message = 'Configuration error', configKey = null) {
    super(message, 500, 'CONFIGURATION_ERROR', false); // Not operational
    this.configKey = configKey;
  }

  toJSON() {
    return {
      ...super.toJSON(),
      configKey: this.configKey
    };
  }
}

/**
 * Security error (403)
 */
export class SecurityError extends AppError {
  constructor(message = 'Security violation detected', violationType = null) {
    super(message, 403, 'SECURITY_VIOLATION');
    this.violationType = violationType;
  }

  toJSON() {
    return {
      ...super.toJSON(),
      violationType: this.violationType
    };
  }
}

/**
 * Timeout error (408)
 */
export class TimeoutError extends AppError {
  constructor(message = 'Request timeout', timeout = null) {
    super(message, 408, 'TIMEOUT');
    this.timeout = timeout;
  }

  toJSON() {
    return {
      ...super.toJSON(),
      timeout: this.timeout
    };
  }
}

/**
 * Error factory functions
 */
export const createValidationError = (message, errors = [], field = null) => {
  return new ValidationError(message, errors, field);
};

export const createAuthError = (message = 'Authentication failed') => {
  return new AuthenticationError(message);
};

export const createNotFoundError = (resource = null, message = null) => {
  const defaultMessage = resource ? `${resource} not found` : 'Resource not found';
  return new NotFoundError(message || defaultMessage, resource);
};

export const createConflictError = (field = null, message = null) => {
  const defaultMessage = field ? `${field} already exists` : 'Resource conflict';
  return new ConflictError(message || defaultMessage, field);
};

export const createDatabaseError = (originalError, message = 'Database operation failed') => {
  return new DatabaseError(message, originalError);
};

export const createBusinessLogicError = (message, rule = null) => {
  return new BusinessLogicError(message, rule);
};

/**
 * Error type checking utilities
 */
export const isOperationalError = (error) => {
  return error instanceof AppError && error.isOperational;
};

export const isValidationError = (error) => {
  return error instanceof ValidationError;
};

export const isAuthError = (error) => {
  return error instanceof AuthenticationError || error instanceof AuthorizationError;
};

export const isDatabaseError = (error) => {
  return error instanceof DatabaseError;
};

export const isExternalServiceError = (error) => {
  return error instanceof ExternalServiceError;
};

/**
 * Error severity levels
 */
export const ErrorSeverity = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
};

/**
 * Get error severity based on error type and status code
 * @param {Error} error - Error object
 * @returns {string} Severity level
 */
export const getErrorSeverity = (error) => {
  if (error instanceof AppError) {
    if (error.statusCode >= 500) {
      return ErrorSeverity.CRITICAL;
    } else if (error.statusCode >= 400) {
      return ErrorSeverity.MEDIUM;
    } else {
      return ErrorSeverity.LOW;
    }
  }

  // For non-AppError instances
  return ErrorSeverity.HIGH;
};

export default {
  AppError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  RateLimitError,
  DatabaseError,
  ExternalServiceError,
  BusinessLogicError,
  FileUploadError,
  ConfigurationError,
  SecurityError,
  TimeoutError,
  createValidationError,
  createAuthError,
  createNotFoundError,
  createConflictError,
  createDatabaseError,
  createBusinessLogicError,
  isOperationalError,
  isValidationError,
  isAuthError,
  isDatabaseError,
  isExternalServiceError,
  ErrorSeverity,
  getErrorSeverity
};
