/**
 * Standardized API Response Utilities
 * Provides consistent response formatting across all endpoints
 */

/**
 * Standard API response format
 * @param {boolean} success - Success status
 * @param {string} message - Response message
 * @param {any} data - Response data
 * @param {string} code - Error/success code
 * @param {Object} meta - Additional metadata
 * @returns {Object} Formatted response
 */
export const standardResponse = (success, message, data = null, code = null, meta = {}) => {
  const response = {
    success,
    message,
    timestamp: new Date().toISOString()
  };

  if (data !== null) {
    response.data = data;
  }

  if (code) {
    response.code = code;
  }

  if (Object.keys(meta).length > 0) {
    response.meta = meta;
  }

  return response;
};

/**
 * Success response helper
 * @param {string} message - Success message
 * @param {any} data - Response data
 * @param {Object} meta - Additional metadata
 * @returns {Object} Success response
 */
export const successResponse = (message, data = null, meta = {}) => {
  return standardResponse(true, message, data, null, meta);
};

/**
 * Error response helper
 * @param {string} message - Error message
 * @param {string} code - Error code
 * @param {any} data - Error data
 * @param {Object} meta - Additional metadata
 * @returns {Object} Error response
 */
export const errorResponse = (message, code = 'ERROR', data = null, meta = {}) => {
  return standardResponse(false, message, data, code, meta);
};

/**
 * Validation error response
 * @param {Array} errors - Validation errors
 * @param {string} message - Error message
 * @returns {Object} Validation error response
 */
export const validationErrorResponse = (errors, message = 'Validation failed') => {
  return standardResponse(false, message, null, 'VALIDATION_ERROR', { errors });
};

/**
 * Paginated response helper
 * @param {Array} items - Data items
 * @param {number} page - Current page
 * @param {number} limit - Items per page
 * @param {number} total - Total items count
 * @param {string} message - Response message
 * @returns {Object} Paginated response
 */
export const paginatedResponse = (items, page, limit, total, message = 'Data retrieved successfully') => {
  const totalPages = Math.ceil(total / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  const pagination = {
    currentPage: page,
    totalPages,
    totalItems: total,
    itemsPerPage: limit,
    hasNextPage,
    hasPrevPage,
    nextPage: hasNextPage ? page + 1 : null,
    prevPage: hasPrevPage ? page - 1 : null
  };

  return standardResponse(true, message, items, null, { pagination });
};

/**
 * Created response helper (201)
 * @param {string} message - Success message
 * @param {any} data - Created resource data
 * @param {Object} meta - Additional metadata
 * @returns {Object} Created response
 */
export const createdResponse = (message, data = null, meta = {}) => {
  return standardResponse(true, message, data, 'CREATED', meta);
};

/**
 * Updated response helper
 * @param {string} message - Success message
 * @param {any} data - Updated resource data
 * @param {Object} meta - Additional metadata
 * @returns {Object} Updated response
 */
export const updatedResponse = (message, data = null, meta = {}) => {
  return standardResponse(true, message, data, 'UPDATED', meta);
};

/**
 * Deleted response helper
 * @param {string} message - Success message
 * @param {Object} meta - Additional metadata
 * @returns {Object} Deleted response
 */
export const deletedResponse = (message = 'Resource deleted successfully', meta = {}) => {
  return standardResponse(true, message, null, 'DELETED', meta);
};

/**
 * Not found response helper (404)
 * @param {string} message - Error message
 * @param {string} resource - Resource type
 * @returns {Object} Not found response
 */
export const notFoundResponse = (message = 'Resource not found', resource = null) => {
  const meta = resource ? { resource } : {};
  return standardResponse(false, message, null, 'NOT_FOUND', meta);
};

/**
 * Unauthorized response helper (401)
 * @param {string} message - Error message
 * @returns {Object} Unauthorized response
 */
export const unauthorizedResponse = (message = 'Unauthorized access') => {
  return standardResponse(false, message, null, 'UNAUTHORIZED');
};

/**
 * Forbidden response helper (403)
 * @param {string} message - Error message
 * @returns {Object} Forbidden response
 */
export const forbiddenResponse = (message = 'Access forbidden') => {
  return standardResponse(false, message, null, 'FORBIDDEN');
};

/**
 * Conflict response helper (409)
 * @param {string} message - Error message
 * @param {any} data - Conflict data
 * @returns {Object} Conflict response
 */
export const conflictResponse = (message = 'Resource conflict', data = null) => {
  return standardResponse(false, message, data, 'CONFLICT');
};

/**
 * Internal server error response helper (500)
 * @param {string} message - Error message
 * @param {string} requestId - Request ID for tracking
 * @returns {Object} Internal server error response
 */
export const internalServerErrorResponse = (message = 'Internal server error', requestId = null) => {
  const meta = requestId ? { requestId } : {};
  return standardResponse(false, message, null, 'INTERNAL_SERVER_ERROR', meta);
};

/**
 * Bad request response helper (400)
 * @param {string} message - Error message
 * @param {any} data - Error data
 * @returns {Object} Bad request response
 */
export const badRequestResponse = (message = 'Bad request', data = null) => {
  return standardResponse(false, message, data, 'BAD_REQUEST');
};

/**
 * Too many requests response helper (429)
 * @param {string} message - Error message
 * @param {number} retryAfter - Retry after seconds
 * @returns {Object} Too many requests response
 */
export const tooManyRequestsResponse = (message = 'Too many requests', retryAfter = null) => {
  const meta = retryAfter ? { retryAfter } : {};
  return standardResponse(false, message, null, 'TOO_MANY_REQUESTS', meta);
};

/**
 * Service unavailable response helper (503)
 * @param {string} message - Error message
 * @returns {Object} Service unavailable response
 */
export const serviceUnavailableResponse = (message = 'Service temporarily unavailable') => {
  return standardResponse(false, message, null, 'SERVICE_UNAVAILABLE');
};

/**
 * Express response sender helper
 * @param {Object} res - Express response object
 * @param {number} statusCode - HTTP status code
 * @param {Object} responseData - Response data
 */
export const sendResponse = (res, statusCode, responseData) => {
  res.status(statusCode).json(responseData);
};

/**
 * Express success response sender
 * @param {Object} res - Express response object
 * @param {string} message - Success message
 * @param {any} data - Response data
 * @param {number} statusCode - HTTP status code (default 200)
 * @param {Object} meta - Additional metadata
 */
export const sendSuccess = (res, message, data = null, statusCode = 200, meta = {}) => {
  const response = successResponse(message, data, meta);
  sendResponse(res, statusCode, response);
};

/**
 * Express error response sender
 * @param {Object} res - Express response object
 * @param {string} message - Error message
 * @param {number} statusCode - HTTP status code
 * @param {string} code - Error code
 * @param {any} data - Error data
 * @param {Object} meta - Additional metadata
 */
export const sendError = (res, message, statusCode = 500, code = 'ERROR', data = null, meta = {}) => {
  const response = errorResponse(message, code, data, meta);
  sendResponse(res, statusCode, response);
};

export default {
  standardResponse,
  successResponse,
  errorResponse,
  validationErrorResponse,
  paginatedResponse,
  createdResponse,
  updatedResponse,
  deletedResponse,
  notFoundResponse,
  unauthorizedResponse,
  forbiddenResponse,
  conflictResponse,
  internalServerErrorResponse,
  badRequestResponse,
  tooManyRequestsResponse,
  serviceUnavailableResponse,
  sendResponse,
  sendSuccess,
  sendError
};
