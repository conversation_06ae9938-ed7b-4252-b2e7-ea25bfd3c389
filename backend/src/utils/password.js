import bcrypt from 'bcrypt';
import crypto from 'crypto';
import config from '../config/index.js';

/**
 * Password Utility Functions
 * Handles password hashing, validation, and security
 */

/**
 * Generate salt for password hashing
 * @param {number} rounds - Number of salt rounds (default from config)
 * @returns {Promise<string>} Generated salt
 */
export const generateSalt = async (rounds = config.BCRYPT_ROUNDS) => {
  return await bcrypt.genSalt(rounds);
};

/**
 * Hash password with salt
 * @param {string} password - Plain text password
 * @param {string} salt - Salt for hashing
 * @returns {Promise<string>} Hashed password
 */
export const hashPassword = async (password, salt) => {
  return await bcrypt.hash(password, salt);
};

/**
 * Hash password with automatic salt generation
 * @param {string} password - Plain text password
 * @param {number} rounds - Number of salt rounds
 * @returns {Promise<Object>} Object containing hash and salt
 */
export const hashPasswordWithSalt = async (password, rounds = config.BCRYPT_ROUNDS) => {
  const salt = await generateSalt(rounds);
  const hash = await hashPassword(password, salt);
  
  return {
    hash,
    salt
  };
};

/**
 * Compare password with hash
 * @param {string} password - Plain text password
 * @param {string} hash - Hashed password
 * @returns {Promise<boolean>} True if password matches
 */
export const comparePassword = async (password, hash) => {
  return await bcrypt.compare(password, hash);
};

/**
 * Validate password strength
 * @param {string} password - Password to validate
 * @returns {Object} Validation result
 */
export const validatePasswordStrength = (password) => {
  const result = {
    isValid: true,
    errors: [],
    score: 0,
    suggestions: []
  };

  // Check minimum length
  if (password.length < 8) {
    result.isValid = false;
    result.errors.push('Password must be at least 8 characters long');
  } else {
    result.score += 1;
  }

  // Check for uppercase letters
  if (!/[A-Z]/.test(password)) {
    result.isValid = false;
    result.errors.push('Password must contain at least one uppercase letter');
    result.suggestions.push('Add uppercase letters (A-Z)');
  } else {
    result.score += 1;
  }

  // Check for lowercase letters
  if (!/[a-z]/.test(password)) {
    result.isValid = false;
    result.errors.push('Password must contain at least one lowercase letter');
    result.suggestions.push('Add lowercase letters (a-z)');
  } else {
    result.score += 1;
  }

  // Check for numbers
  if (!/\d/.test(password)) {
    result.isValid = false;
    result.errors.push('Password must contain at least one number');
    result.suggestions.push('Add numbers (0-9)');
  } else {
    result.score += 1;
  }

  // Check for special characters
  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    result.isValid = false;
    result.errors.push('Password must contain at least one special character');
    result.suggestions.push('Add special characters (!@#$%^&*)');
  } else {
    result.score += 1;
  }

  // Check for common patterns
  const commonPatterns = [
    /123456/,
    /password/i,
    /qwerty/i,
    /admin/i,
    /letmein/i
  ];

  for (const pattern of commonPatterns) {
    if (pattern.test(password)) {
      result.isValid = false;
      result.errors.push('Password contains common patterns that are easily guessed');
      result.suggestions.push('Avoid common words and patterns');
      break;
    }
  }

  // Check for repeated characters
  if (/(.)\1{2,}/.test(password)) {
    result.score -= 1;
    result.suggestions.push('Avoid repeating the same character multiple times');
  }

  // Bonus points for length
  if (password.length >= 12) {
    result.score += 1;
  }
  if (password.length >= 16) {
    result.score += 1;
  }

  // Calculate strength level
  if (result.score >= 6) {
    result.strength = 'very-strong';
  } else if (result.score >= 5) {
    result.strength = 'strong';
  } else if (result.score >= 3) {
    result.strength = 'medium';
  } else if (result.score >= 2) {
    result.strength = 'weak';
  } else {
    result.strength = 'very-weak';
  }

  return result;
};

/**
 * Generate secure random password
 * @param {number} length - Password length (default 16)
 * @param {Object} options - Generation options
 * @returns {string} Generated password
 */
export const generateSecurePassword = (length = 16, options = {}) => {
  const defaults = {
    includeUppercase: true,
    includeLowercase: true,
    includeNumbers: true,
    includeSpecialChars: true,
    excludeSimilar: true // Exclude similar looking characters (0, O, l, 1, etc.)
  };

  const config = { ...defaults, ...options };
  
  let charset = '';
  
  if (config.includeLowercase) {
    charset += config.excludeSimilar ? 'abcdefghijkmnopqrstuvwxyz' : 'abcdefghijklmnopqrstuvwxyz';
  }
  
  if (config.includeUppercase) {
    charset += config.excludeSimilar ? 'ABCDEFGHJKLMNPQRSTUVWXYZ' : 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  }
  
  if (config.includeNumbers) {
    charset += config.excludeSimilar ? '23456789' : '0123456789';
  }
  
  if (config.includeSpecialChars) {
    charset += '!@#$%^&*()_+-=[]{}|;:,.<>?';
  }

  if (charset === '') {
    throw new Error('At least one character type must be included');
  }

  let password = '';
  for (let i = 0; i < length; i++) {
    const randomIndex = crypto.randomInt(0, charset.length);
    password += charset[randomIndex];
  }

  return password;
};

/**
 * Generate password reset token
 * @returns {string} Secure random token
 */
export const generatePasswordResetToken = () => {
  return crypto.randomBytes(32).toString('hex');
};

/**
 * Generate email verification token
 * @returns {string} Secure random token
 */
export const generateEmailVerificationToken = () => {
  return crypto.randomBytes(32).toString('hex');
};

/**
 * Hash sensitive data (like tokens)
 * @param {string} data - Data to hash
 * @returns {string} SHA-256 hash
 */
export const hashSensitiveData = (data) => {
  return crypto.createHash('sha256').update(data).digest('hex');
};

/**
 * Generate secure random string
 * @param {number} length - String length
 * @returns {string} Random string
 */
export const generateSecureRandomString = (length = 32) => {
  return crypto.randomBytes(Math.ceil(length / 2)).toString('hex').slice(0, length);
};

/**
 * Check if password has been compromised (basic check)
 * @param {string} password - Password to check
 * @returns {boolean} True if password appears compromised
 */
export const isPasswordCompromised = (password) => {
  // List of most common compromised passwords
  const compromisedPasswords = [
    '123456', 'password', '123456789', '12345678', '12345',
    '111111', '1234567', 'sunshine', 'qwerty', 'iloveyou',
    'princess', 'admin', 'welcome', '666666', 'abc123',
    'football', '123123', 'monkey', '654321', '!@#$%^&*',
    'charlie', 'aa123456', 'donald', 'password1', 'qwerty123'
  ];

  return compromisedPasswords.includes(password.toLowerCase());
};

/**
 * Create password policy checker
 * @param {Object} policy - Password policy configuration
 * @returns {Function} Policy checker function
 */
export const createPasswordPolicyChecker = (policy = {}) => {
  const defaultPolicy = {
    minLength: 8,
    maxLength: 128,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
    preventCommonPasswords: true,
    preventUserInfo: true // Prevent using user's name, email, etc.
  };

  const activePolicy = { ...defaultPolicy, ...policy };

  return (password, userInfo = {}) => {
    const result = validatePasswordStrength(password);
    
    // Apply custom policy rules
    if (password.length < activePolicy.minLength) {
      result.isValid = false;
      result.errors.push(`Password must be at least ${activePolicy.minLength} characters long`);
    }

    if (password.length > activePolicy.maxLength) {
      result.isValid = false;
      result.errors.push(`Password must be no more than ${activePolicy.maxLength} characters long`);
    }

    if (activePolicy.preventCommonPasswords && isPasswordCompromised(password)) {
      result.isValid = false;
      result.errors.push('Password is commonly used and not secure');
    }

    if (activePolicy.preventUserInfo && userInfo) {
      const userInfoValues = Object.values(userInfo).filter(Boolean);
      for (const info of userInfoValues) {
        if (password.toLowerCase().includes(info.toLowerCase())) {
          result.isValid = false;
          result.errors.push('Password should not contain personal information');
          break;
        }
      }
    }

    return result;
  };
};

export default {
  generateSalt,
  hashPassword,
  hashPasswordWithSalt,
  comparePassword,
  validatePasswordStrength,
  generateSecurePassword,
  generatePasswordResetToken,
  generateEmailVerificationToken,
  hashSensitiveData,
  generateSecureRandomString,
  isPasswordCompromised,
  createPasswordPolicyChecker
};
