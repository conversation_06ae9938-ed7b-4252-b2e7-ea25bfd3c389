import jwt from 'jsonwebtoken';
import config from '../config/index.js';

/**
 * JWT Utility Functions
 * Handles JWT token generation, verification, and management
 */

/**
 * Generate access token
 * @param {Object} payload - Token payload
 * @returns {string} JWT token
 */
export const generateAccessToken = (payload) => {
  return jwt.sign(
    payload,
    config.JWT_SECRET,
    {
      expiresIn: config.JWT_EXPIRES_IN,
      issuer: 'harvest-profit-pro',
      audience: 'harvest-profit-pro-users'
    }
  );
};

/**
 * Generate refresh token
 * @param {Object} payload - Token payload
 * @returns {string} JWT refresh token
 */
export const generateRefreshToken = (payload) => {
  return jwt.sign(
    { ...payload, type: 'refresh' },
    config.JWT_SECRET,
    {
      expiresIn: config.JWT_REFRESH_EXPIRES_IN,
      issuer: 'harvest-profit-pro',
      audience: 'harvest-profit-pro-users'
    }
  );
};

/**
 * Verify JWT token
 * @param {string} token - JWT token to verify
 * @returns {Object} Decoded token payload
 * @throws {Error} If token is invalid
 */
export const verifyToken = (token) => {
  try {
    return jwt.verify(token, config.JWT_SECRET, {
      issuer: 'harvest-profit-pro',
      audience: 'harvest-profit-pro-users'
    });
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      throw new Error('Token has expired');
    } else if (error.name === 'JsonWebTokenError') {
      throw new Error('Invalid token');
    } else if (error.name === 'NotBeforeError') {
      throw new Error('Token not active');
    } else {
      throw new Error('Token verification failed');
    }
  }
};

/**
 * Decode JWT token without verification (for debugging)
 * @param {string} token - JWT token to decode
 * @returns {Object} Decoded token payload
 */
export const decodeToken = (token) => {
  return jwt.decode(token);
};

/**
 * Extract token from Authorization header
 * @param {string} authHeader - Authorization header value
 * @returns {string|null} Extracted token or null
 */
export const extractTokenFromHeader = (authHeader) => {
  if (!authHeader) return null;
  
  const parts = authHeader.split(' ');
  if (parts.length !== 2 || parts[0] !== 'Bearer') {
    return null;
  }
  
  return parts[1];
};

/**
 * Check if token is expired
 * @param {string} token - JWT token to check
 * @returns {boolean} True if token is expired
 */
export const isTokenExpired = (token) => {
  try {
    const decoded = jwt.decode(token);
    if (!decoded || !decoded.exp) return true;
    
    const currentTime = Math.floor(Date.now() / 1000);
    return decoded.exp < currentTime;
  } catch (error) {
    return true;
  }
};

/**
 * Get token expiration time
 * @param {string} token - JWT token
 * @returns {Date|null} Expiration date or null
 */
export const getTokenExpiration = (token) => {
  try {
    const decoded = jwt.decode(token);
    if (!decoded || !decoded.exp) return null;
    
    return new Date(decoded.exp * 1000);
  } catch (error) {
    return null;
  }
};

/**
 * Generate token pair (access + refresh)
 * @param {Object} user - User object
 * @returns {Object} Token pair
 */
export const generateTokenPair = (user) => {
  const payload = {
    id: user.id,
    email: user.email,
    role: user.role,
    farmId: user.farms?.[0]?.id || null
  };
  
  const accessToken = generateAccessToken(payload);
  const refreshToken = generateRefreshToken({ id: user.id });
  
  return {
    accessToken,
    refreshToken,
    expiresIn: config.JWT_EXPIRES_IN,
    tokenType: 'Bearer'
  };
};

/**
 * Validate refresh token and generate new access token
 * @param {string} refreshToken - Refresh token
 * @param {Object} user - User object for new token
 * @returns {Object} New token pair
 */
export const refreshAccessToken = (refreshToken, user) => {
  try {
    const decoded = verifyToken(refreshToken);
    
    if (decoded.type !== 'refresh') {
      throw new Error('Invalid refresh token type');
    }
    
    if (decoded.id !== user.id) {
      throw new Error('Refresh token user mismatch');
    }
    
    return generateTokenPair(user);
  } catch (error) {
    throw new Error(`Refresh token validation failed: ${error.message}`);
  }
};

/**
 * Create token blacklist entry (for logout)
 * @param {string} token - Token to blacklist
 * @returns {Object} Blacklist entry
 */
export const createTokenBlacklistEntry = (token) => {
  const decoded = decodeToken(token);
  return {
    token,
    userId: decoded?.id,
    expiresAt: getTokenExpiration(token),
    blacklistedAt: new Date()
  };
};

/**
 * Validate token permissions for role-based access
 * @param {Object} decoded - Decoded token payload
 * @param {string|Array} requiredRoles - Required role(s)
 * @returns {boolean} True if user has required permissions
 */
export const validateTokenPermissions = (decoded, requiredRoles) => {
  if (!decoded || !decoded.role) return false;
  
  const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];
  return roles.includes(decoded.role);
};

/**
 * Get user context from token
 * @param {string} token - JWT token
 * @returns {Object|null} User context or null
 */
export const getUserContextFromToken = (token) => {
  try {
    const decoded = verifyToken(token);
    return {
      id: decoded.id,
      email: decoded.email,
      role: decoded.role,
      farmId: decoded.farmId
    };
  } catch (error) {
    return null;
  }
};

export default {
  generateAccessToken,
  generateRefreshToken,
  verifyToken,
  decodeToken,
  extractTokenFromHeader,
  isTokenExpired,
  getTokenExpiration,
  generateTokenPair,
  refreshAccessToken,
  createTokenBlacklistEntry,
  validateTokenPermissions,
  getUserContextFromToken
};
