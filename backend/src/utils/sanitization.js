/**
 * Input Sanitization Utilities
 * Provides functions to sanitize user inputs and prevent XSS attacks
 */

/**
 * Sanitize a string input
 * @param {string} input - Input string to sanitize
 * @param {Object} options - Sanitization options
 * @returns {string} Sanitized string
 */
export const sanitizeInput = (input, options = {}) => {
  if (typeof input !== 'string') {
    return input;
  }

  const defaults = {
    allowHtml: false,
    allowScripts: false,
    maxLength: 10000,
    trimWhitespace: true
  };

  const config = { ...defaults, ...options };

  let sanitized = input;

  // Trim whitespace if enabled
  if (config.trimWhitespace) {
    sanitized = sanitized.trim();
  }

  // Enforce maximum length
  if (sanitized.length > config.maxLength) {
    sanitized = sanitized.substring(0, config.maxLength);
  }

  // Remove or escape HTML if not allowed
  if (!config.allowHtml) {
    sanitized = escapeHtml(sanitized);
  } else {
    // If HTML is allowed, still remove dangerous elements
    sanitized = removeDangerousHtml(sanitized);
  }

  // Always remove scripts regardless of allowHtml setting
  if (!config.allowScripts) {
    sanitized = removeScripts(sanitized);
  }

  // Remove null bytes and other control characters
  sanitized = sanitized.replace(/\x00/g, '');
  sanitized = sanitized.replace(/[\x01-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');

  return sanitized;
};

/**
 * Escape HTML characters
 * @param {string} input - Input string
 * @returns {string} HTML-escaped string
 */
export const escapeHtml = (input) => {
  const htmlEscapeMap = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#x27;',
    '/': '&#x2F;',
    '`': '&#x60;',
    '=': '&#x3D;'
  };

  return input.replace(/[&<>"'`=\/]/g, (char) => htmlEscapeMap[char]);
};

/**
 * Remove dangerous HTML elements and attributes
 * @param {string} input - Input string
 * @returns {string} Sanitized HTML string
 */
export const removeDangerousHtml = (input) => {
  // Remove script tags and their content
  input = input.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
  
  // Remove iframe tags
  input = input.replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '');
  
  // Remove object and embed tags
  input = input.replace(/<(object|embed)\b[^<]*(?:(?!<\/\1>)<[^<]*)*<\/\1>/gi, '');
  
  // Remove form tags
  input = input.replace(/<form\b[^<]*(?:(?!<\/form>)<[^<]*)*<\/form>/gi, '');
  
  // Remove dangerous attributes
  input = input.replace(/\s*on\w+\s*=\s*["'][^"']*["']/gi, ''); // onclick, onload, etc.
  input = input.replace(/\s*javascript\s*:/gi, '');
  input = input.replace(/\s*vbscript\s*:/gi, '');
  input = input.replace(/\s*data\s*:/gi, '');
  
  return input;
};

/**
 * Remove script tags and JavaScript
 * @param {string} input - Input string
 * @returns {string} String without scripts
 */
export const removeScripts = (input) => {
  // Remove script tags
  input = input.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
  
  // Remove javascript: URLs
  input = input.replace(/javascript\s*:/gi, '');
  
  // Remove event handlers
  input = input.replace(/\s*on\w+\s*=\s*["'][^"']*["']/gi, '');
  
  return input;
};

/**
 * Sanitize an object recursively
 * @param {Object} obj - Object to sanitize
 * @param {Object} options - Sanitization options
 * @returns {Object} Sanitized object
 */
export const sanitizeObject = (obj, options = {}) => {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (typeof obj === 'string') {
    return sanitizeInput(obj, options);
  }

  if (typeof obj === 'number' || typeof obj === 'boolean') {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => sanitizeObject(item, options));
  }

  if (typeof obj === 'object') {
    const sanitized = {};
    for (const [key, value] of Object.entries(obj)) {
      // Sanitize the key as well
      const sanitizedKey = sanitizeInput(key, { allowHtml: false, maxLength: 100 });
      sanitized[sanitizedKey] = sanitizeObject(value, options);
    }
    return sanitized;
  }

  return obj;
};

/**
 * Sanitize email address
 * @param {string} email - Email address
 * @returns {string} Sanitized email
 */
export const sanitizeEmail = (email) => {
  if (typeof email !== 'string') {
    return email;
  }

  // Basic email sanitization
  let sanitized = email.toLowerCase().trim();
  
  // Remove dangerous characters
  sanitized = sanitized.replace(/[<>'"&]/g, '');
  
  // Basic email format validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(sanitized)) {
    throw new Error('Invalid email format');
  }

  return sanitized;
};

/**
 * Sanitize phone number
 * @param {string} phone - Phone number
 * @returns {string} Sanitized phone number
 */
export const sanitizePhone = (phone) => {
  if (typeof phone !== 'string') {
    return phone;
  }

  // Remove all non-digit characters except + and -
  let sanitized = phone.replace(/[^\d+\-\s()]/g, '');
  
  // Trim whitespace
  sanitized = sanitized.trim();
  
  return sanitized;
};

/**
 * Sanitize URL
 * @param {string} url - URL to sanitize
 * @returns {string} Sanitized URL
 */
export const sanitizeUrl = (url) => {
  if (typeof url !== 'string') {
    return url;
  }

  let sanitized = url.trim();

  // Check for dangerous protocols
  const dangerousProtocols = ['javascript:', 'vbscript:', 'data:', 'file:'];
  const lowerUrl = sanitized.toLowerCase();
  
  for (const protocol of dangerousProtocols) {
    if (lowerUrl.startsWith(protocol)) {
      throw new Error('Dangerous URL protocol detected');
    }
  }

  // Only allow http, https, and relative URLs
  if (sanitized.includes('://')) {
    if (!sanitized.match(/^https?:\/\//)) {
      throw new Error('Only HTTP and HTTPS URLs are allowed');
    }
  }

  return sanitized;
};

/**
 * Sanitize filename
 * @param {string} filename - Filename to sanitize
 * @returns {string} Sanitized filename
 */
export const sanitizeFilename = (filename) => {
  if (typeof filename !== 'string') {
    return filename;
  }

  // Remove path traversal attempts
  let sanitized = filename.replace(/\.\./g, '');
  
  // Remove dangerous characters
  sanitized = sanitized.replace(/[<>:"/\\|?*\x00-\x1f]/g, '');
  
  // Trim whitespace and dots
  sanitized = sanitized.trim().replace(/^\.+|\.+$/g, '');
  
  // Ensure filename is not empty
  if (!sanitized) {
    sanitized = 'unnamed_file';
  }

  // Limit length
  if (sanitized.length > 255) {
    const extension = sanitized.substring(sanitized.lastIndexOf('.'));
    const name = sanitized.substring(0, 255 - extension.length);
    sanitized = name + extension;
  }

  return sanitized;
};

/**
 * Sanitize SQL-like input (for search queries, etc.)
 * @param {string} input - Input string
 * @returns {string} Sanitized string
 */
export const sanitizeSqlInput = (input) => {
  if (typeof input !== 'string') {
    return input;
  }

  // Remove SQL injection patterns
  let sanitized = input;
  
  // Remove SQL comments
  sanitized = sanitized.replace(/--.*$/gm, '');
  sanitized = sanitized.replace(/\/\*[\s\S]*?\*\//g, '');
  
  // Remove dangerous SQL keywords (case insensitive)
  const dangerousKeywords = [
    'DROP', 'DELETE', 'INSERT', 'UPDATE', 'CREATE', 'ALTER',
    'EXEC', 'EXECUTE', 'UNION', 'SCRIPT', 'DECLARE'
  ];
  
  for (const keyword of dangerousKeywords) {
    const regex = new RegExp(`\\b${keyword}\\b`, 'gi');
    sanitized = sanitized.replace(regex, '');
  }
  
  // Remove semicolons
  sanitized = sanitized.replace(/;/g, '');
  
  return sanitized.trim();
};

export default {
  sanitizeInput,
  escapeHtml,
  removeDangerousHtml,
  removeScripts,
  sanitizeObject,
  sanitizeEmail,
  sanitizePhone,
  sanitizeUrl,
  sanitizeFilename,
  sanitizeSqlInput
};
