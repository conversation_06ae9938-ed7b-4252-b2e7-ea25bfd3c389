import { extractTokenFromHeader, verifyToken, validateTokenPermissions } from '../utils/jwt.js';
import db from '../models/index.js';

const { User, UserSession } = db;

/**
 * Authentication middleware to verify JWT tokens
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
export const authenticate = async (req, res, next) => {
  try {
    // Extract token from Authorization header
    const token = extractTokenFromHeader(req.headers.authorization);
    
    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Access token is required',
        code: 'TOKEN_MISSING'
      });
    }

    // Verify token
    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error) {
      return res.status(401).json({
        success: false,
        message: error.message,
        code: 'TOKEN_INVALID'
      });
    }

    // Check if user exists and is active
    const user = await User.findByPk(decoded.id, {
      include: [
        {
          model: db.Farm,
          as: 'farms',
          attributes: ['id', 'name']
        }
      ]
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }

    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        message: 'User account is deactivated',
        code: 'USER_DEACTIVATED'
      });
    }

    // Check if session is valid (optional - for enhanced security)
    if (UserSession) {
      const session = await UserSession.findOne({
        where: {
          userId: user.id,
          sessionToken: token,
          isActive: true
        }
      });

      if (!session) {
        return res.status(401).json({
          success: false,
          message: 'Session not found or expired',
          code: 'SESSION_INVALID'
        });
      }

      // Check if session is expired
      if (session.expiresAt < new Date()) {
        await session.update({ isActive: false });
        return res.status(401).json({
          success: false,
          message: 'Session has expired',
          code: 'SESSION_EXPIRED'
        });
      }
    }

    // Attach user and token info to request
    req.user = user;
    req.token = token;
    req.decoded = decoded;

    next();
  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(500).json({
      success: false,
      message: 'Authentication failed',
      code: 'AUTH_ERROR'
    });
  }
};

/**
 * Authorization middleware to check user roles
 * @param {string|Array} roles - Required role(s)
 * @returns {Function} Middleware function
 */
export const authorize = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    const userRole = req.user.role;
    const requiredRoles = Array.isArray(roles) ? roles : [roles];

    if (!requiredRoles.includes(userRole)) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions',
        code: 'INSUFFICIENT_PERMISSIONS',
        required: requiredRoles,
        current: userRole
      });
    }

    next();
  };
};

/**
 * Optional authentication middleware (doesn't fail if no token)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
export const optionalAuth = async (req, res, next) => {
  try {
    const token = extractTokenFromHeader(req.headers.authorization);
    
    if (!token) {
      return next();
    }

    try {
      const decoded = verifyToken(token);
      const user = await User.findByPk(decoded.id);
      
      if (user && user.isActive) {
        req.user = user;
        req.token = token;
        req.decoded = decoded;
      }
    } catch (error) {
      // Ignore token errors in optional auth
      console.warn('Optional auth token error:', error.message);
    }

    next();
  } catch (error) {
    console.error('Optional authentication error:', error);
    next();
  }
};

/**
 * Farm ownership middleware - ensures user owns the farm
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
export const requireFarmOwnership = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    const farmId = req.params.farmId || req.body.farmId || req.query.farmId;
    
    if (!farmId) {
      return res.status(400).json({
        success: false,
        message: 'Farm ID is required',
        code: 'FARM_ID_REQUIRED'
      });
    }

    // Admin can access any farm
    if (req.user.role === 'admin') {
      return next();
    }

    // Check if user owns the farm
    const farm = await db.Farm.findOne({
      where: {
        id: farmId,
        ownerId: req.user.id
      }
    });

    if (!farm) {
      return res.status(403).json({
        success: false,
        message: 'Access denied: You do not own this farm',
        code: 'FARM_ACCESS_DENIED'
      });
    }

    req.farm = farm;
    next();
  } catch (error) {
    console.error('Farm ownership check error:', error);
    return res.status(500).json({
      success: false,
      message: 'Farm ownership verification failed',
      code: 'FARM_OWNERSHIP_ERROR'
    });
  }
};

/**
 * Rate limiting for authentication endpoints
 * @param {number} maxAttempts - Maximum attempts per window
 * @param {number} windowMs - Time window in milliseconds
 * @returns {Function} Middleware function
 */
export const authRateLimit = (maxAttempts = 5, windowMs = 15 * 60 * 1000) => {
  const attempts = new Map();

  return (req, res, next) => {
    const key = req.ip || req.connection.remoteAddress;
    const now = Date.now();
    
    // Clean old entries
    for (const [ip, data] of attempts.entries()) {
      if (now - data.firstAttempt > windowMs) {
        attempts.delete(ip);
      }
    }

    const userAttempts = attempts.get(key);
    
    if (!userAttempts) {
      attempts.set(key, {
        count: 1,
        firstAttempt: now
      });
      return next();
    }

    if (now - userAttempts.firstAttempt > windowMs) {
      // Reset window
      attempts.set(key, {
        count: 1,
        firstAttempt: now
      });
      return next();
    }

    if (userAttempts.count >= maxAttempts) {
      return res.status(429).json({
        success: false,
        message: 'Too many authentication attempts. Please try again later.',
        code: 'RATE_LIMIT_EXCEEDED',
        retryAfter: Math.ceil((windowMs - (now - userAttempts.firstAttempt)) / 1000)
      });
    }

    userAttempts.count++;
    next();
  };
};

/**
 * Middleware to check if user's email is verified
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
export const requireEmailVerification = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required',
      code: 'AUTH_REQUIRED'
    });
  }

  if (!req.user.emailVerified) {
    return res.status(403).json({
      success: false,
      message: 'Email verification required',
      code: 'EMAIL_NOT_VERIFIED'
    });
  }

  next();
};

/**
 * Middleware to log authentication events
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
export const logAuthEvent = (eventType) => {
  return async (req, res, next) => {
    try {
      // Log authentication event (implement based on your logging strategy)
      const logData = {
        eventType,
        userId: req.user?.id,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        timestamp: new Date()
      };

      // You can implement actual logging here (database, file, external service)
      console.log('Auth Event:', logData);

      next();
    } catch (error) {
      console.error('Auth logging error:', error);
      next(); // Don't fail the request due to logging errors
    }
  };
};

export default {
  authenticate,
  authorize,
  optionalAuth,
  requireFarmOwnership,
  authRateLimit,
  requireEmailVerification,
  logAuthEvent
};
