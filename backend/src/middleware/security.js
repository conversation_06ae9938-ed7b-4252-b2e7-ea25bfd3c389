import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import { body, query, param, validationResult } from 'express-validator';
import config from '../config/index.js';
import { sanitizeInput, sanitizeObject } from '../utils/sanitization.js';
import { errorResponse } from '../utils/response.js';

/**
 * Security Middleware Collection
 * Implements various security measures for the API
 */

/**
 * Enhanced Helmet configuration for security headers
 */
export const securityHeaders = helmet({
  // Content Security Policy
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:", "blob:"],
      connectSrc: ["'self'", "https://api.harvestprofitpro.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
      childSrc: ["'none'"],
      workerSrc: ["'self'"],
      manifestSrc: ["'self'"],
      baseUri: ["'self'"],
      formAction: ["'self'"]
    },
    reportOnly: config.NODE_ENV === 'development'
  },

  // HTTP Strict Transport Security
  hsts: {
    maxAge: 31536000, // 1 year
    includeSubDomains: true,
    preload: true
  },

  // X-Frame-Options
  frameguard: {
    action: 'deny'
  },

  // X-Content-Type-Options
  noSniff: true,

  // X-XSS-Protection
  xssFilter: true,

  // Referrer Policy
  referrerPolicy: {
    policy: 'strict-origin-when-cross-origin'
  },

  // Hide X-Powered-By header
  hidePoweredBy: true,

  // DNS Prefetch Control
  dnsPrefetchControl: {
    allow: false
  },

  // Expect-CT
  expectCt: {
    maxAge: 86400,
    enforce: config.NODE_ENV === 'production'
  },

  // Feature Policy / Permissions Policy
  permittedCrossDomainPolicies: false,

  // Cross-Origin Embedder Policy
  crossOriginEmbedderPolicy: false,

  // Cross-Origin Opener Policy
  crossOriginOpenerPolicy: {
    policy: 'same-origin'
  },

  // Cross-Origin Resource Policy
  crossOriginResourcePolicy: {
    policy: 'cross-origin'
  }
});

/**
 * Input sanitization middleware
 * Sanitizes all request inputs to prevent XSS and injection attacks
 */
export const sanitizeInputs = (req, res, next) => {
  try {
    // Sanitize request body
    if (req.body && typeof req.body === 'object') {
      req.body = sanitizeObject(req.body);
    }

    // Sanitize query parameters
    if (req.query && typeof req.query === 'object') {
      req.query = sanitizeObject(req.query);
    }

    // Sanitize URL parameters
    if (req.params && typeof req.params === 'object') {
      req.params = sanitizeObject(req.params);
    }

    next();
  } catch (error) {
    console.error('Input sanitization error:', error);
    res.status(400).json(errorResponse('Invalid input data', 'SANITIZATION_ERROR'));
  }
};

/**
 * SQL injection prevention middleware
 * Additional layer of protection against SQL injection
 */
export const preventSQLInjection = (req, res, next) => {
  const sqlInjectionPatterns = [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,
    /(\b(OR|AND)\s+\d+\s*=\s*\d+)/gi,
    /(--|\/\*|\*\/|;)/g,
    /(\b(CHAR|NCHAR|VARCHAR|NVARCHAR)\s*\(\s*\d+\s*\))/gi,
    /(\b(CAST|CONVERT|SUBSTRING|ASCII|CHAR_LENGTH)\s*\()/gi
  ];

  const checkForSQLInjection = (obj, path = '') => {
    for (const [key, value] of Object.entries(obj)) {
      const currentPath = path ? `${path}.${key}` : key;
      
      if (typeof value === 'string') {
        for (const pattern of sqlInjectionPatterns) {
          if (pattern.test(value)) {
            throw new Error(`Potential SQL injection detected in ${currentPath}`);
          }
        }
      } else if (typeof value === 'object' && value !== null) {
        checkForSQLInjection(value, currentPath);
      }
    }
  };

  try {
    // Check request body
    if (req.body) {
      checkForSQLInjection(req.body);
    }

    // Check query parameters
    if (req.query) {
      checkForSQLInjection(req.query);
    }

    // Check URL parameters
    if (req.params) {
      checkForSQLInjection(req.params);
    }

    next();
  } catch (error) {
    console.warn('SQL injection attempt detected:', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      url: req.originalUrl,
      method: req.method,
      error: error.message
    });

    res.status(400).json(errorResponse('Invalid input detected', 'SECURITY_VIOLATION'));
  }
};

/**
 * XSS protection middleware
 * Additional XSS protection beyond helmet
 */
export const preventXSS = (req, res, next) => {
  const xssPatterns = [
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
    /<img[^>]+src[\\s]*=[\\s]*["\']javascript:/gi,
    /<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi,
    /<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi
  ];

  const checkForXSS = (obj, path = '') => {
    for (const [key, value] of Object.entries(obj)) {
      const currentPath = path ? `${path}.${key}` : key;
      
      if (typeof value === 'string') {
        for (const pattern of xssPatterns) {
          if (pattern.test(value)) {
            throw new Error(`Potential XSS detected in ${currentPath}`);
          }
        }
      } else if (typeof value === 'object' && value !== null) {
        checkForXSS(value, currentPath);
      }
    }
  };

  try {
    // Check request body
    if (req.body) {
      checkForXSS(req.body);
    }

    // Check query parameters
    if (req.query) {
      checkForXSS(req.query);
    }

    next();
  } catch (error) {
    console.warn('XSS attempt detected:', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      url: req.originalUrl,
      method: req.method,
      error: error.message
    });

    res.status(400).json(errorResponse('Invalid input detected', 'SECURITY_VIOLATION'));
  }
};

/**
 * CSRF protection middleware
 * Simple CSRF protection for state-changing operations
 */
export const csrfProtection = (req, res, next) => {
  // Skip CSRF for GET, HEAD, OPTIONS requests
  if (['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
    return next();
  }

  // Skip CSRF for API endpoints with proper authentication
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer ')) {
    return next();
  }

  // Check for CSRF token in headers
  const csrfToken = req.headers['x-csrf-token'] || req.headers['x-xsrf-token'];
  
  if (!csrfToken) {
    return res.status(403).json(errorResponse('CSRF token required', 'CSRF_TOKEN_MISSING'));
  }

  // Validate CSRF token (simplified implementation)
  // In production, you would validate against a stored token
  if (csrfToken.length < 32) {
    return res.status(403).json(errorResponse('Invalid CSRF token', 'CSRF_TOKEN_INVALID'));
  }

  next();
};

/**
 * Request size limiting middleware
 */
export const limitRequestSize = (maxSize = '10mb') => {
  return (req, res, next) => {
    const contentLength = parseInt(req.headers['content-length'] || '0');
    const maxSizeBytes = typeof maxSize === 'string' ? 
      parseInt(maxSize) * (maxSize.includes('mb') ? 1024 * 1024 : 1024) : 
      maxSize;

    if (contentLength > maxSizeBytes) {
      return res.status(413).json(errorResponse('Request entity too large', 'REQUEST_TOO_LARGE'));
    }

    next();
  };
};

/**
 * Security audit logging middleware
 */
export const securityAuditLog = (req, res, next) => {
  // Log security-relevant events
  const securityEvent = {
    timestamp: new Date().toISOString(),
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    method: req.method,
    url: req.originalUrl,
    userId: req.user?.id,
    headers: {
      origin: req.get('Origin'),
      referer: req.get('Referer'),
      authorization: req.get('Authorization') ? 'present' : 'absent'
    }
  };

  // Log to security audit system (implement based on your logging strategy)
  console.log('Security Audit:', securityEvent);

  next();
};

/**
 * API key validation middleware (for external integrations)
 */
export const validateApiKey = (req, res, next) => {
  const apiKey = req.headers['x-api-key'];
  
  if (!apiKey) {
    return res.status(401).json(errorResponse('API key required', 'API_KEY_MISSING'));
  }

  // Validate API key (implement your API key validation logic)
  // This is a simplified example
  if (apiKey.length < 32) {
    return res.status(401).json(errorResponse('Invalid API key', 'API_KEY_INVALID'));
  }

  next();
};

/**
 * Honeypot middleware to detect bots
 */
export const honeypot = (req, res, next) => {
  // Check for honeypot field in forms
  if (req.body && req.body.honeypot) {
    console.warn('Honeypot triggered:', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      url: req.originalUrl
    });
    
    // Return success to not reveal the honeypot
    return res.status(200).json({ success: true, message: 'Request processed' });
  }

  next();
};

export default {
  securityHeaders,
  sanitizeInputs,
  preventSQLInjection,
  preventXSS,
  csrfProtection,
  limitRequestSize,
  securityAuditLog,
  validateApiKey,
  honeypot
};
