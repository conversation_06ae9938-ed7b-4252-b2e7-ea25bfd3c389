import rateLimit from 'express-rate-limit';
import config from '../config/index.js';

/**
 * Rate Limiting Middleware
 * Implements various rate limiting strategies for different endpoints
 */

// Global rate limit for all requests
const globalRateLimit = rateLimit({
  windowMs: config.RATE_LIMIT_WINDOW_MS, // 15 minutes
  max: config.RATE_LIMIT_MAX_REQUESTS, // 100 requests per window
  message: {
    success: false,
    message: 'Too many requests from this IP, please try again later',
    code: 'RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  keyGenerator: (req) => {
    // Use IP address as the key
    return req.ip || req.connection.remoteAddress;
  },
  handler: (req, res) => {
    res.status(429).json({
      success: false,
      message: 'Too many requests from this IP, please try again later',
      code: 'RATE_LIMIT_EXCEEDED',
      retryAfter: Math.round(config.RATE_LIMIT_WINDOW_MS / 1000)
    });
  }
});

// Strict rate limit for authentication endpoints
const authRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: config.RATE_LIMIT_AUTH_MAX_REQUESTS, // 5 requests per window
  message: {
    success: false,
    message: 'Too many authentication attempts, please try again later',
    code: 'AUTH_RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    // Combine IP and email for auth endpoints
    const email = req.body?.email || '';
    return `${req.ip}-${email}`;
  },
  handler: (req, res) => {
    res.status(429).json({
      success: false,
      message: 'Too many authentication attempts, please try again later',
      code: 'AUTH_RATE_LIMIT_EXCEEDED',
      retryAfter: Math.round(15 * 60) // 15 minutes in seconds
    });
  }
});

// API rate limit for general API endpoints
const apiRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 200, // 200 requests per window for API endpoints
  message: {
    success: false,
    message: 'Too many API requests, please try again later',
    code: 'API_RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    // Use user ID if authenticated, otherwise IP
    return req.user?.id || req.ip;
  }
});

// Upload rate limit for file uploads
const uploadRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // 10 uploads per hour
  message: {
    success: false,
    message: 'Too many file uploads, please try again later',
    code: 'UPLOAD_RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    return req.user?.id || req.ip;
  }
});

// Password reset rate limit
const passwordResetRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // 3 password reset requests per hour
  message: {
    success: false,
    message: 'Too many password reset attempts, please try again later',
    code: 'PASSWORD_RESET_RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    const email = req.body?.email || '';
    return `password-reset-${req.ip}-${email}`;
  }
});

// Create user rate limit (for admin endpoints)
const createUserRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 20, // 20 user creations per hour
  message: {
    success: false,
    message: 'Too many user creation attempts, please try again later',
    code: 'CREATE_USER_RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    return req.user?.id || req.ip;
  }
});

// Dynamic rate limit based on user role
const roleBasedRateLimit = (adminMax = 1000, managerMax = 500, userMax = 200) => {
  return rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: (req) => {
      if (req.user?.role === 'admin') return adminMax;
      if (req.user?.role === 'manager') return managerMax;
      return userMax;
    },
    message: {
      success: false,
      message: 'Rate limit exceeded for your user role',
      code: 'ROLE_RATE_LIMIT_EXCEEDED'
    },
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: (req) => {
      return req.user?.id || req.ip;
    }
  });
};

// Skip rate limiting for certain conditions
const skipRateLimit = (req) => {
  // Skip for health checks
  if (req.path === '/health' || req.path === '/api/status') {
    return true;
  }
  
  // Skip for admin users in development
  if (config.NODE_ENV === 'development' && req.user?.role === 'admin') {
    return true;
  }
  
  return false;
};

// Apply skip logic to all rate limiters
const rateLimiters = [
  globalRateLimit,
  authRateLimit,
  apiRateLimit,
  uploadRateLimit,
  passwordResetRateLimit,
  createUserRateLimit
];

rateLimiters.forEach(limiter => {
  limiter.skip = skipRateLimit;
});

export default {
  global: globalRateLimit,
  auth: authRateLimit,
  api: apiRateLimit,
  upload: uploadRateLimit,
  passwordReset: passwordResetRateLimit,
  createUser: createUserRateLimit,
  roleBased: roleBasedRateLimit
};
