import logger, { loggerUtils } from '../utils/logger.js';
import { 
  AppError, 
  ValidationError, 
  AuthenticationError, 
  AuthorizationError,
  NotFoundError,
  ConflictError,
  RateLimitError,
  DatabaseError,
  ExternalServiceError,
  isOperationalError,
  getErrorSeverity
} from '../utils/errors.js';
import { errorResponse } from '../utils/response.js';
import config from '../config/index.js';

/**
 * Global Error Handling Middleware
 * Handles all errors in the application with proper logging and response formatting
 */

/**
 * Handle Sequelize database errors
 * @param {Error} error - Sequelize error
 * @returns {AppError} Formatted application error
 */
const handleSequelizeError = (error) => {
  let message = 'Database operation failed';
  let code = 'DATABASE_ERROR';
  let statusCode = 500;

  switch (error.name) {
    case 'SequelizeValidationError':
      message = 'Validation failed';
      code = 'VALIDATION_ERROR';
      statusCode = 400;
      const validationErrors = error.errors.map(err => ({
        field: err.path,
        message: err.message,
        value: err.value
      }));
      return new ValidationError(message, validationErrors);

    case 'SequelizeUniqueConstraintError':
      message = 'Resource already exists';
      code = 'CONFLICT';
      statusCode = 409;
      const field = error.errors[0]?.path || 'unknown';
      return new ConflictError(message, field);

    case 'SequelizeForeignKeyConstraintError':
      message = 'Referenced resource not found';
      code = 'FOREIGN_KEY_ERROR';
      statusCode = 400;
      break;

    case 'SequelizeConnectionError':
    case 'SequelizeConnectionRefusedError':
    case 'SequelizeHostNotFoundError':
      message = 'Database connection failed';
      code = 'DATABASE_CONNECTION_ERROR';
      statusCode = 503;
      break;

    case 'SequelizeTimeoutError':
      message = 'Database operation timed out';
      code = 'DATABASE_TIMEOUT';
      statusCode = 408;
      break;

    default:
      message = error.message || 'Database operation failed';
  }

  return new DatabaseError(message, error);
};

/**
 * Handle JWT errors
 * @param {Error} error - JWT error
 * @returns {AppError} Formatted application error
 */
const handleJWTError = (error) => {
  let message = 'Authentication failed';
  let code = 'AUTH_FAILED';

  switch (error.name) {
    case 'JsonWebTokenError':
      message = 'Invalid token';
      code = 'INVALID_TOKEN';
      break;
    case 'TokenExpiredError':
      message = 'Token has expired';
      code = 'TOKEN_EXPIRED';
      break;
    case 'NotBeforeError':
      message = 'Token not active';
      code = 'TOKEN_NOT_ACTIVE';
      break;
  }

  return new AuthenticationError(message, code);
};

/**
 * Handle validation errors from express-validator
 * @param {Array} errors - Validation errors array
 * @returns {ValidationError} Formatted validation error
 */
const handleExpressValidatorErrors = (errors) => {
  const formattedErrors = errors.map(error => ({
    field: error.param || error.path,
    message: error.msg,
    value: error.value,
    location: error.location
  }));

  return new ValidationError('Request validation failed', formattedErrors);
};

/**
 * Handle Multer file upload errors
 * @param {Error} error - Multer error
 * @returns {AppError} Formatted application error
 */
const handleMulterError = (error) => {
  let message = 'File upload failed';
  let code = 'FILE_UPLOAD_ERROR';

  switch (error.code) {
    case 'LIMIT_FILE_SIZE':
      message = 'File size too large';
      code = 'FILE_TOO_LARGE';
      break;
    case 'LIMIT_FILE_COUNT':
      message = 'Too many files';
      code = 'TOO_MANY_FILES';
      break;
    case 'LIMIT_UNEXPECTED_FILE':
      message = 'Unexpected file field';
      code = 'UNEXPECTED_FILE';
      break;
    case 'LIMIT_FIELD_KEY':
      message = 'Field name too long';
      code = 'FIELD_NAME_TOO_LONG';
      break;
    case 'LIMIT_FIELD_VALUE':
      message = 'Field value too long';
      code = 'FIELD_VALUE_TOO_LONG';
      break;
    case 'LIMIT_FIELD_COUNT':
      message = 'Too many fields';
      code = 'TOO_MANY_FIELDS';
      break;
  }

  return new AppError(message, 400, code);
};

/**
 * Send error response to client
 * @param {Error} error - Error object
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const sendErrorResponse = (error, req, res) => {
  const isDevelopment = config.NODE_ENV === 'development';
  
  // Prepare error response
  const response = {
    success: false,
    message: error.message,
    code: error.code || 'INTERNAL_ERROR',
    timestamp: new Date().toISOString(),
    requestId: req.id
  };

  // Add stack trace in development
  if (isDevelopment && error.stack) {
    response.stack = error.stack;
  }

  // Add additional error details for specific error types
  if (error instanceof ValidationError && error.errors) {
    response.errors = error.errors;
  }

  if (error instanceof AuthorizationError && error.requiredRole) {
    response.requiredRole = error.requiredRole;
  }

  if (error instanceof NotFoundError && error.resource) {
    response.resource = error.resource;
  }

  if (error instanceof ConflictError && error.conflictingField) {
    response.conflictingField = error.conflictingField;
  }

  if (error instanceof RateLimitError && error.retryAfter) {
    response.retryAfter = error.retryAfter;
    res.set('Retry-After', error.retryAfter);
  }

  // Send response
  res.status(error.statusCode || 500).json(response);
};

/**
 * Log error with appropriate level and context
 * @param {Error} error - Error object
 * @param {Object} req - Express request object
 */
const logError = (error, req) => {
  const severity = getErrorSeverity(error);
  const context = {
    requestId: req.id,
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id,
    body: req.method !== 'GET' ? req.body : undefined,
    query: req.query,
    params: req.params,
    severity
  };

  // Log based on severity
  if (severity === 'critical' || error.statusCode >= 500) {
    loggerUtils.logError(error, context);
  } else if (severity === 'high' || error.statusCode >= 400) {
    logger.warn('Client Error', {
      message: error.message,
      statusCode: error.statusCode,
      code: error.code,
      context
    });
  } else {
    logger.info('Request Error', {
      message: error.message,
      statusCode: error.statusCode,
      code: error.code,
      context
    });
  }

  // Log security events
  if (error instanceof AuthenticationError || error instanceof AuthorizationError) {
    loggerUtils.logSecurityEvent('auth_error', {
      errorType: error.constructor.name,
      message: error.message,
      code: error.code
    }, req);
  }
};

/**
 * Main error handling middleware
 * @param {Error} err - Error object
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const errorHandler = (err, req, res, next) => {
  let error = err;

  // Handle different error types
  if (err.name && err.name.startsWith('Sequelize')) {
    error = handleSequelizeError(err);
  } else if (err.name && ['JsonWebTokenError', 'TokenExpiredError', 'NotBeforeError'].includes(err.name)) {
    error = handleJWTError(err);
  } else if (err.code && err.code.startsWith('LIMIT_')) {
    error = handleMulterError(err);
  } else if (err.type === 'entity.parse.failed') {
    error = new ValidationError('Invalid JSON in request body');
  } else if (err.type === 'entity.too.large') {
    error = new AppError('Request entity too large', 413, 'REQUEST_TOO_LARGE');
  } else if (!(err instanceof AppError)) {
    // Convert unknown errors to AppError
    error = new AppError(
      config.NODE_ENV === 'development' ? err.message : 'Internal server error',
      500,
      'INTERNAL_ERROR',
      false // Not operational
    );
  }

  // Log the error
  logError(error, req);

  // Send error response
  sendErrorResponse(error, req, res);
};

/**
 * Handle 404 errors for undefined routes
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const notFoundHandler = (req, res, next) => {
  const error = new NotFoundError(`Route ${req.originalUrl} not found`);
  next(error);
};

/**
 * Handle uncaught exceptions
 * @param {Error} error - Uncaught exception
 */
const handleUncaughtException = (error) => {
  logger.error('Uncaught Exception', {
    message: error.message,
    stack: error.stack,
    name: error.name
  });

  // Graceful shutdown
  process.exit(1);
};

/**
 * Handle unhandled promise rejections
 * @param {any} reason - Rejection reason
 * @param {Promise} promise - Rejected promise
 */
const handleUnhandledRejection = (reason, promise) => {
  logger.error('Unhandled Promise Rejection', {
    reason: reason instanceof Error ? reason.message : reason,
    stack: reason instanceof Error ? reason.stack : undefined,
    promise: promise.toString()
  });

  // Graceful shutdown
  process.exit(1);
};

// Set up global error handlers
process.on('uncaughtException', handleUncaughtException);
process.on('unhandledRejection', handleUnhandledRejection);

export default errorHandler;
export { notFoundHandler, handleUncaughtException, handleUnhandledRejection };
