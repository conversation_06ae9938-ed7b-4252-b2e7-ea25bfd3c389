/**
 * Model Associations Configuration
 * This file defines all relationships between Sequelize models
 */

const setupAssociations = (db) => {
  const {
    User,
    Farm,
    Field,
    Crop,
    ExpenseCategory,
    Expense,
    Revenue,
    Budget,
    InputCategory,
    Input,
    InputApplication,
    Equipment,
    EquipmentMaintenance,
    EquipmentUsage,
    GrainContract,
    MarketPrice,
    GrainInventory,
    CropPlan,
    PlantingSchedule,
    HarvestSchedule,
    CapitalTransaction,
    CapitalBudget,
    UserSession,
    Notification,
    AuditLog
  } = db;

  // User Associations
  if (User) {
    // User owns farms
    User.hasMany(Farm, {
      foreignKey: 'ownerId',
      as: 'farms',
      onDelete: 'CASCADE'
    });

    // User creates various records
    User.hasMany(Expense, {
      foreignKey: 'createdBy',
      as: 'expenses'
    });

    User.hasMany(Revenue, {
      foreignKey: 'createdBy',
      as: 'revenueRecords'
    });

    User.hasMany(Budget, {
      foreignKey: 'createdBy',
      as: 'budgets'
    });

    // User has sessions and notifications
    User.hasMany(UserSession, {
      foreignKey: 'userId',
      as: 'sessions',
      onDelete: 'CASCADE'
    });

    User.hasMany(Notification, {
      foreignKey: 'userId',
      as: 'notifications',
      onDelete: 'CASCADE'
    });

    User.hasMany(AuditLog, {
      foreignKey: 'userId',
      as: 'auditLogs'
    });
  }

  // Farm Associations
  if (Farm) {
    // Farm belongs to User (owner)
    Farm.belongsTo(User, {
      foreignKey: 'ownerId',
      as: 'owner'
    });

    // Farm has many related entities
    Farm.hasMany(Field, {
      foreignKey: 'farmId',
      as: 'fields',
      onDelete: 'CASCADE'
    });

    Farm.hasMany(Expense, {
      foreignKey: 'farmId',
      as: 'expenses',
      onDelete: 'CASCADE'
    });

    Farm.hasMany(Revenue, {
      foreignKey: 'farmId',
      as: 'revenueRecords',
      onDelete: 'CASCADE'
    });

    Farm.hasMany(Budget, {
      foreignKey: 'farmId',
      as: 'budgets',
      onDelete: 'CASCADE'
    });

    Farm.hasMany(Equipment, {
      foreignKey: 'farmId',
      as: 'equipment',
      onDelete: 'CASCADE'
    });

    Farm.hasMany(Input, {
      foreignKey: 'farmId',
      as: 'inputs',
      onDelete: 'CASCADE'
    });

    Farm.hasMany(InputApplication, {
      foreignKey: 'farmId',
      as: 'inputApplications',
      onDelete: 'CASCADE'
    });

    Farm.hasMany(GrainContract, {
      foreignKey: 'farmId',
      as: 'grainContracts',
      onDelete: 'CASCADE'
    });

    Farm.hasMany(GrainInventory, {
      foreignKey: 'farmId',
      as: 'grainInventory',
      onDelete: 'CASCADE'
    });

    Farm.hasMany(CropPlan, {
      foreignKey: 'farmId',
      as: 'cropPlans',
      onDelete: 'CASCADE'
    });

    Farm.hasMany(CapitalTransaction, {
      foreignKey: 'farmId',
      as: 'capitalTransactions',
      onDelete: 'CASCADE'
    });

    Farm.hasMany(CapitalBudget, {
      foreignKey: 'farmId',
      as: 'capitalBudgets',
      onDelete: 'CASCADE'
    });
  }

  // Field Associations
  if (Field) {
    // Field belongs to Farm
    Field.belongsTo(Farm, {
      foreignKey: 'farmId',
      as: 'farm'
    });

    // Field has many related entities
    Field.hasMany(Expense, {
      foreignKey: 'fieldId',
      as: 'expenses'
    });

    Field.hasMany(Revenue, {
      foreignKey: 'fieldId',
      as: 'revenueRecords'
    });

    Field.hasMany(CropPlan, {
      foreignKey: 'fieldId',
      as: 'cropPlans',
      onDelete: 'CASCADE'
    });

    Field.hasMany(InputApplication, {
      foreignKey: 'fieldId',
      as: 'inputApplications',
      onDelete: 'CASCADE'
    });

    Field.hasMany(EquipmentUsage, {
      foreignKey: 'fieldId',
      as: 'equipmentUsage'
    });

    Field.hasMany(GrainInventory, {
      foreignKey: 'fieldId',
      as: 'grainInventory'
    });
  }

  // Crop Associations
  if (Crop) {
    Crop.hasMany(Revenue, {
      foreignKey: 'cropId',
      as: 'revenueRecords'
    });

    Crop.hasMany(GrainContract, {
      foreignKey: 'cropId',
      as: 'grainContracts'
    });

    Crop.hasMany(MarketPrice, {
      foreignKey: 'cropId',
      as: 'marketPrices',
      onDelete: 'CASCADE'
    });

    Crop.hasMany(GrainInventory, {
      foreignKey: 'cropId',
      as: 'grainInventory'
    });

    Crop.hasMany(CropPlan, {
      foreignKey: 'cropId',
      as: 'cropPlans'
    });
  }

  // Expense Category Associations
  if (ExpenseCategory) {
    // Self-referencing for parent/child categories
    ExpenseCategory.belongsTo(ExpenseCategory, {
      foreignKey: 'parentCategoryId',
      as: 'parentCategory'
    });

    ExpenseCategory.hasMany(ExpenseCategory, {
      foreignKey: 'parentCategoryId',
      as: 'subcategories'
    });

    ExpenseCategory.hasMany(Expense, {
      foreignKey: 'categoryId',
      as: 'expenses'
    });

    ExpenseCategory.hasMany(Budget, {
      foreignKey: 'categoryId',
      as: 'budgets'
    });
  }

  // Expense Associations
  if (Expense) {
    Expense.belongsTo(Farm, {
      foreignKey: 'farmId',
      as: 'farm'
    });

    Expense.belongsTo(Field, {
      foreignKey: 'fieldId',
      as: 'field'
    });

    Expense.belongsTo(ExpenseCategory, {
      foreignKey: 'categoryId',
      as: 'category'
    });

    Expense.belongsTo(User, {
      foreignKey: 'createdBy',
      as: 'creator'
    });
  }

  // Revenue Associations
  if (Revenue) {
    Revenue.belongsTo(Farm, {
      foreignKey: 'farmId',
      as: 'farm'
    });

    Revenue.belongsTo(Field, {
      foreignKey: 'fieldId',
      as: 'field'
    });

    Revenue.belongsTo(Crop, {
      foreignKey: 'cropId',
      as: 'crop'
    });

    Revenue.belongsTo(User, {
      foreignKey: 'createdBy',
      as: 'creator'
    });
  }

  // Budget Associations
  if (Budget) {
    Budget.belongsTo(Farm, {
      foreignKey: 'farmId',
      as: 'farm'
    });

    Budget.belongsTo(Field, {
      foreignKey: 'fieldId',
      as: 'field'
    });

    Budget.belongsTo(ExpenseCategory, {
      foreignKey: 'categoryId',
      as: 'category'
    });

    Budget.belongsTo(User, {
      foreignKey: 'createdBy',
      as: 'creator'
    });
  }

  // Input Category Associations
  if (InputCategory) {
    InputCategory.hasMany(Input, {
      foreignKey: 'categoryId',
      as: 'inputs'
    });
  }

  // Input Associations
  if (Input) {
    Input.belongsTo(Farm, {
      foreignKey: 'farmId',
      as: 'farm'
    });

    Input.belongsTo(InputCategory, {
      foreignKey: 'categoryId',
      as: 'category'
    });

    Input.hasMany(InputApplication, {
      foreignKey: 'inputId',
      as: 'applications',
      onDelete: 'CASCADE'
    });
  }

  // Input Application Associations
  if (InputApplication) {
    InputApplication.belongsTo(Farm, {
      foreignKey: 'farmId',
      as: 'farm'
    });

    InputApplication.belongsTo(Field, {
      foreignKey: 'fieldId',
      as: 'field'
    });

    InputApplication.belongsTo(Input, {
      foreignKey: 'inputId',
      as: 'input'
    });

    InputApplication.belongsTo(User, {
      foreignKey: 'createdBy',
      as: 'creator'
    });
  }

  // Equipment Associations
  if (Equipment) {
    Equipment.belongsTo(Farm, {
      foreignKey: 'farmId',
      as: 'farm'
    });

    Equipment.hasMany(EquipmentMaintenance, {
      foreignKey: 'equipmentId',
      as: 'maintenanceRecords',
      onDelete: 'CASCADE'
    });

    Equipment.hasMany(EquipmentUsage, {
      foreignKey: 'equipmentId',
      as: 'usageRecords',
      onDelete: 'CASCADE'
    });
  }

  // Equipment Maintenance Associations
  if (EquipmentMaintenance) {
    EquipmentMaintenance.belongsTo(Equipment, {
      foreignKey: 'equipmentId',
      as: 'equipment'
    });

    EquipmentMaintenance.belongsTo(User, {
      foreignKey: 'createdBy',
      as: 'creator'
    });
  }

  // Equipment Usage Associations
  if (EquipmentUsage) {
    EquipmentUsage.belongsTo(Equipment, {
      foreignKey: 'equipmentId',
      as: 'equipment'
    });

    EquipmentUsage.belongsTo(Field, {
      foreignKey: 'fieldId',
      as: 'field'
    });

    EquipmentUsage.belongsTo(User, {
      foreignKey: 'createdBy',
      as: 'creator'
    });
  }

  console.log('✅ Model associations configured successfully');
};

export default setupAssociations;
