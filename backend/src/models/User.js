import { DataTypes } from 'sequelize';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import config from '../config/index.js';

const User = (sequelize) => {
  const UserModel = sequelize.define('User', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    email: {
      type: DataTypes.STRING(255),
      allowNull: false,
      unique: {
        name: 'users_email_unique',
        msg: 'Email address already exists'
      },
      validate: {
        isEmail: {
          msg: 'Must be a valid email address'
        },
        notEmpty: {
          msg: 'Email is required'
        }
      }
    },
    passwordHash: {
      type: DataTypes.STRING(255),
      allowNull: false,
      field: 'password_hash'
    },
    salt: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    firstName: {
      type: DataTypes.STRING(100),
      allowNull: false,
      field: 'first_name',
      validate: {
        notEmpty: {
          msg: 'First name is required'
        },
        len: {
          args: [1, 100],
          msg: 'First name must be between 1 and 100 characters'
        }
      }
    },
    lastName: {
      type: DataTypes.STRING(100),
      allowNull: false,
      field: 'last_name',
      validate: {
        notEmpty: {
          msg: 'Last name is required'
        },
        len: {
          args: [1, 100],
          msg: 'Last name must be between 1 and 100 characters'
        }
      }
    },
    phone: {
      type: DataTypes.STRING(20),
      allowNull: true,
      validate: {
        is: {
          args: /^[\+]?[1-9][\d]{0,15}$/,
          msg: 'Phone number must be valid'
        }
      }
    },
    role: {
      type: DataTypes.ENUM('admin', 'manager', 'user'),
      defaultValue: 'user',
      validate: {
        isIn: {
          args: [['admin', 'manager', 'user']],
          msg: 'Role must be admin, manager, or user'
        }
      }
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      field: 'is_active'
    },
    emailVerified: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      field: 'email_verified'
    },
    lastLogin: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'last_login'
    }
  }, {
    tableName: 'users',
    timestamps: true,
    paranoid: true, // Enable soft deletes
    underscored: true,
    indexes: [
      {
        unique: true,
        fields: ['email']
      },
      {
        fields: ['role']
      },
      {
        fields: ['is_active']
      }
    ],
    hooks: {
      beforeCreate: async (user) => {
        if (user.passwordHash) {
          const salt = await bcrypt.genSalt(config.BCRYPT_ROUNDS);
          user.salt = salt;
          user.passwordHash = await bcrypt.hash(user.passwordHash, salt);
        }
      },
      beforeUpdate: async (user) => {
        if (user.changed('passwordHash')) {
          const salt = await bcrypt.genSalt(config.BCRYPT_ROUNDS);
          user.salt = salt;
          user.passwordHash = await bcrypt.hash(user.passwordHash, salt);
        }
      }
    }
  });

  // Instance methods
  UserModel.prototype.validatePassword = async function(password) {
    return await bcrypt.compare(password, this.passwordHash);
  };

  UserModel.prototype.generateToken = function() {
    return jwt.sign(
      {
        id: this.id,
        email: this.email,
        role: this.role
      },
      config.JWT_SECRET,
      {
        expiresIn: config.JWT_EXPIRES_IN
      }
    );
  };

  UserModel.prototype.generateRefreshToken = function() {
    return jwt.sign(
      {
        id: this.id,
        type: 'refresh'
      },
      config.JWT_SECRET,
      {
        expiresIn: config.JWT_REFRESH_EXPIRES_IN
      }
    );
  };

  UserModel.prototype.toJSON = function() {
    const values = { ...this.get() };
    delete values.passwordHash;
    delete values.salt;
    return values;
  };

  UserModel.prototype.getFullName = function() {
    return `${this.firstName} ${this.lastName}`;
  };

  UserModel.prototype.updateLastLogin = async function() {
    this.lastLogin = new Date();
    await this.save();
  };

  // Class methods
  UserModel.findByEmail = async function(email) {
    return await this.findOne({
      where: { email: email.toLowerCase() }
    });
  };

  UserModel.findActiveUsers = async function() {
    return await this.findAll({
      where: { isActive: true }
    });
  };

  UserModel.findByRole = async function(role) {
    return await this.findAll({
      where: { role }
    });
  };

  // Associations
  UserModel.associate = function(models) {
    // User owns farms
    UserModel.hasMany(models.Farm, {
      foreignKey: 'ownerId',
      as: 'farms'
    });

    // User creates expenses
    UserModel.hasMany(models.Expense, {
      foreignKey: 'createdBy',
      as: 'expenses'
    });

    // User creates revenue records
    UserModel.hasMany(models.Revenue, {
      foreignKey: 'createdBy',
      as: 'revenueRecords'
    });

    // User has sessions
    UserModel.hasMany(models.UserSession, {
      foreignKey: 'userId',
      as: 'sessions'
    });

    // User has notifications
    UserModel.hasMany(models.Notification, {
      foreignKey: 'userId',
      as: 'notifications'
    });
  };

  return UserModel;
};

export default User;
