import { DataTypes } from 'sequelize';

const ExpenseCategory = (sequelize) => {
  const ExpenseCategoryModel = sequelize.define('ExpenseCategory', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: {
        name: 'expense_categories_name_unique',
        msg: 'Category name already exists'
      },
      validate: {
        notEmpty: {
          msg: 'Category name is required'
        },
        len: {
          args: [1, 100],
          msg: 'Category name must be between 1 and 100 characters'
        }
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    parentCategoryId: {
      type: DataTypes.UUID,
      allowNull: true,
      field: 'parent_category_id',
      references: {
        model: 'expense_categories',
        key: 'id'
      }
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      field: 'is_active'
    }
  }, {
    tableName: 'expense_categories',
    timestamps: true,
    paranoid: true, // Enable soft deletes
    underscored: true,
    indexes: [
      {
        unique: true,
        fields: ['name']
      },
      {
        fields: ['parent_category_id']
      },
      {
        fields: ['is_active']
      }
    ]
  });

  // Instance methods
  ExpenseCategoryModel.prototype.getSubcategories = async function() {
    return await ExpenseCategoryModel.findAll({
      where: { parentCategoryId: this.id, isActive: true },
      order: [['name', 'ASC']]
    });
  };

  ExpenseCategoryModel.prototype.getAllSubcategories = async function() {
    const subcategories = await this.getSubcategories();
    let allSubcategories = [...subcategories];
    
    for (const subcategory of subcategories) {
      const nestedSubcategories = await subcategory.getAllSubcategories();
      allSubcategories = allSubcategories.concat(nestedSubcategories);
    }
    
    return allSubcategories;
  };

  ExpenseCategoryModel.prototype.getTotalExpenses = async function(farmId = null, year = null) {
    const subcategoryIds = (await this.getAllSubcategories()).map(sub => sub.id);
    const categoryIds = [this.id, ...subcategoryIds];
    
    let whereClause = { categoryId: { [sequelize.Op.in]: categoryIds } };
    
    if (farmId) whereClause.farmId = farmId;
    if (year) {
      whereClause.expenseDate = {
        [sequelize.Op.gte]: `${year}-01-01`,
        [sequelize.Op.lt]: `${year + 1}-01-01`
      };
    }
    
    const expenses = await this.getExpenses({
      where: whereClause
    });
    
    return expenses.reduce((total, expense) => total + parseFloat(expense.amount || 0), 0);
  };

  ExpenseCategoryModel.prototype.getExpenseCount = async function(farmId = null, year = null) {
    const subcategoryIds = (await this.getAllSubcategories()).map(sub => sub.id);
    const categoryIds = [this.id, ...subcategoryIds];
    
    let whereClause = { categoryId: { [sequelize.Op.in]: categoryIds } };
    
    if (farmId) whereClause.farmId = farmId;
    if (year) {
      whereClause.expenseDate = {
        [sequelize.Op.gte]: `${year}-01-01`,
        [sequelize.Op.lt]: `${year + 1}-01-01`
      };
    }
    
    return await this.countExpenses({
      where: whereClause
    });
  };

  ExpenseCategoryModel.prototype.isParentCategory = function() {
    return this.parentCategoryId === null;
  };

  ExpenseCategoryModel.prototype.getFullPath = async function() {
    if (this.parentCategoryId) {
      const parent = await ExpenseCategoryModel.findByPk(this.parentCategoryId);
      if (parent) {
        const parentPath = await parent.getFullPath();
        return `${parentPath} > ${this.name}`;
      }
    }
    return this.name;
  };

  // Class methods
  ExpenseCategoryModel.findActive = async function() {
    return await this.findAll({
      where: { isActive: true },
      order: [['name', 'ASC']]
    });
  };

  ExpenseCategoryModel.findParentCategories = async function() {
    return await this.findAll({
      where: { 
        parentCategoryId: null,
        isActive: true 
      },
      order: [['name', 'ASC']]
    });
  };

  ExpenseCategoryModel.findByParent = async function(parentId) {
    return await this.findAll({
      where: { 
        parentCategoryId: parentId,
        isActive: true 
      },
      order: [['name', 'ASC']]
    });
  };

  ExpenseCategoryModel.findHierarchy = async function() {
    const parentCategories = await this.findParentCategories();
    
    const hierarchy = await Promise.all(
      parentCategories.map(async (parent) => {
        const subcategories = await parent.getSubcategories();
        return {
          ...parent.toJSON(),
          subcategories: subcategories.map(sub => sub.toJSON())
        };
      })
    );
    
    return hierarchy;
  };

  ExpenseCategoryModel.findWithExpenseTotals = async function(farmId = null, year = null) {
    const categories = await this.findActive();
    
    const categoriesWithTotals = await Promise.all(
      categories.map(async (category) => {
        const totalExpenses = await category.getTotalExpenses(farmId, year);
        const expenseCount = await category.getExpenseCount(farmId, year);
        
        return {
          ...category.toJSON(),
          totalExpenses,
          expenseCount
        };
      })
    );
    
    return categoriesWithTotals;
  };

  ExpenseCategoryModel.createDefault = async function() {
    const defaultCategories = [
      { name: 'Seeds', description: 'Seed costs for planting' },
      { name: 'Fertilizer', description: 'Fertilizer and nutrient costs' },
      { name: 'Pesticides', description: 'Herbicides, insecticides, and fungicides' },
      { name: 'Fuel', description: 'Fuel costs for equipment and vehicles' },
      { name: 'Equipment Maintenance', description: 'Maintenance and repair costs for equipment' },
      { name: 'Labor', description: 'Labor costs including wages and benefits' },
      { name: 'Insurance', description: 'Crop and equipment insurance' },
      { name: 'Utilities', description: 'Electricity, water, and other utilities' }
    ];
    
    const createdCategories = [];
    for (const categoryData of defaultCategories) {
      try {
        const category = await this.create(categoryData);
        createdCategories.push(category);
      } catch (error) {
        // Category might already exist, skip
        console.warn(`Category ${categoryData.name} already exists or error creating:`, error.message);
      }
    }
    
    return createdCategories;
  };

  // Associations
  ExpenseCategoryModel.associate = function(models) {
    // Self-referencing association for parent/child categories
    ExpenseCategoryModel.belongsTo(ExpenseCategoryModel, {
      foreignKey: 'parentCategoryId',
      as: 'parentCategory'
    });

    ExpenseCategoryModel.hasMany(ExpenseCategoryModel, {
      foreignKey: 'parentCategoryId',
      as: 'subcategories'
    });

    // Category has many Expenses
    ExpenseCategoryModel.hasMany(models.Expense, {
      foreignKey: 'categoryId',
      as: 'expenses'
    });

    // Category has many Budgets
    ExpenseCategoryModel.hasMany(models.Budget, {
      foreignKey: 'categoryId',
      as: 'budgets'
    });
  };

  return ExpenseCategoryModel;
};

export default ExpenseCategory;
