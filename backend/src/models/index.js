import { Sequelize } from 'sequelize';
import config from '../config/database.js';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const env = process.env.NODE_ENV || 'development';
const dbConfig = config[env];

// Initialize Sequelize
let sequelize;
if (dbConfig.use_env_variable) {
  sequelize = new Sequelize(process.env[dbConfig.use_env_variable], dbConfig);
} else {
  sequelize = new Sequelize(
    dbConfig.database,
    dbConfig.username,
    dbConfig.password,
    dbConfig
  );
}

const db = {};

// Import all model files dynamically
const modelFiles = fs.readdirSync(__dirname)
  .filter(file => {
    return (
      file.indexOf('.') !== 0 &&
      file !== 'index.js' &&
      file !== 'associations.js' &&
      file.slice(-3) === '.js'
    );
  });

// Load all models
for (const file of modelFiles) {
  const modelPath = join(__dirname, file);
  const { default: model } = await import(modelPath);
  const modelInstance = model(sequelize, Sequelize.DataTypes);
  db[modelInstance.name] = modelInstance;
}

// Set up associations
Object.keys(db).forEach(modelName => {
  if (db[modelName].associate) {
    db[modelName].associate(db);
  }
});

// Import and execute associations
try {
  const { default: setupAssociations } = await import('./associations.js');
  setupAssociations(db);
} catch (error) {
  console.warn('Associations file not found or error loading:', error.message);
}

db.sequelize = sequelize;
db.Sequelize = Sequelize;

// Test database connection
const testConnection = async () => {
  try {
    await sequelize.authenticate();
    console.log('✅ Database connection has been established successfully.');
  } catch (error) {
    console.error('❌ Unable to connect to the database:', error);
    throw error;
  }
};

// Sync database (only in development)
const syncDatabase = async (force = false) => {
  try {
    if (env === 'development') {
      await sequelize.sync({ force, alter: !force });
      console.log('✅ Database synchronized successfully.');
    }
  } catch (error) {
    console.error('❌ Error synchronizing database:', error);
    throw error;
  }
};

// Close database connection
const closeConnection = async () => {
  try {
    await sequelize.close();
    console.log('✅ Database connection closed successfully.');
  } catch (error) {
    console.error('❌ Error closing database connection:', error);
    throw error;
  }
};

// Export database utilities
db.testConnection = testConnection;
db.syncDatabase = syncDatabase;
db.closeConnection = closeConnection;

export default db;
