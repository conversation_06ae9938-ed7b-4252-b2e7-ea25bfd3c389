import { DataTypes } from 'sequelize';

const Expense = (sequelize) => {
  const ExpenseModel = sequelize.define('Expense', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    farmId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'farm_id',
      references: {
        model: 'farms',
        key: 'id'
      }
    },
    fieldId: {
      type: DataTypes.UUID,
      allowNull: true,
      field: 'field_id',
      references: {
        model: 'fields',
        key: 'id'
      }
    },
    categoryId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'category_id',
      references: {
        model: 'expense_categories',
        key: 'id'
      }
    },
    amount: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: false,
      validate: {
        min: {
          args: 0.01,
          msg: 'Amount must be greater than 0'
        },
        isDecimal: {
          msg: 'Amount must be a valid decimal number'
        }
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    expenseDate: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      field: 'expense_date',
      validate: {
        isDate: {
          msg: 'Expense date must be a valid date'
        },
        notInFuture(value) {
          if (new Date(value) > new Date()) {
            throw new Error('Expense date cannot be in the future');
          }
        }
      }
    },
    vendor: {
      type: DataTypes.STRING(255),
      allowNull: true,
      validate: {
        len: {
          args: [0, 255],
          msg: 'Vendor name must be less than 255 characters'
        }
      }
    },
    receiptUrl: {
      type: DataTypes.STRING(500),
      allowNull: true,
      field: 'receipt_url',
      validate: {
        isUrl: {
          msg: 'Receipt URL must be a valid URL'
        }
      }
    },
    paymentMethod: {
      type: DataTypes.STRING(50),
      allowNull: true,
      field: 'payment_method',
      validate: {
        isIn: {
          args: [['cash', 'check', 'credit_card', 'debit_card', 'bank_transfer', 'other']],
          msg: 'Payment method must be one of: cash, check, credit_card, debit_card, bank_transfer, other'
        }
      }
    },
    isRecurring: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      field: 'is_recurring'
    },
    recurringFrequency: {
      type: DataTypes.STRING(20),
      allowNull: true,
      field: 'recurring_frequency',
      validate: {
        isIn: {
          args: [['weekly', 'monthly', 'quarterly', 'annually']],
          msg: 'Recurring frequency must be one of: weekly, monthly, quarterly, annually'
        },
        requiresRecurring(value) {
          if (value && !this.isRecurring) {
            throw new Error('Recurring frequency can only be set if expense is recurring');
          }
        }
      }
    },
    tags: {
      type: DataTypes.JSONB,
      allowNull: true,
      validate: {
        isValidTags(value) {
          if (value && !Array.isArray(value)) {
            throw new Error('Tags must be an array');
          }
        }
      }
    },
    createdBy: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'created_by',
      references: {
        model: 'users',
        key: 'id'
      }
    }
  }, {
    tableName: 'expenses',
    timestamps: true,
    paranoid: true, // Enable soft deletes
    underscored: true,
    indexes: [
      {
        fields: ['farm_id']
      },
      {
        fields: ['field_id']
      },
      {
        fields: ['category_id']
      },
      {
        fields: ['expense_date']
      },
      {
        fields: ['amount']
      },
      {
        fields: ['created_by']
      }
    ]
  });

  // Instance methods
  ExpenseModel.prototype.getAmountPerAcre = async function() {
    if (this.fieldId) {
      const field = await this.getField();
      if (field && field.acres > 0) {
        return parseFloat(this.amount) / parseFloat(field.acres);
      }
    }
    return 0;
  };

  ExpenseModel.prototype.isOverBudget = async function() {
    const currentYear = new Date(this.expenseDate).getFullYear();
    const budget = await this.getBudget({
      where: {
        farmId: this.farmId,
        categoryId: this.categoryId,
        budgetYear: currentYear
      }
    });
    
    if (budget) {
      return parseFloat(budget.actualAmount) > parseFloat(budget.budgetedAmount);
    }
    
    return false;
  };

  ExpenseModel.prototype.addToTags = function(newTags) {
    const currentTags = this.tags || [];
    const tagsToAdd = Array.isArray(newTags) ? newTags : [newTags];
    const uniqueTags = [...new Set([...currentTags, ...tagsToAdd])];
    this.tags = uniqueTags;
    return this.save();
  };

  ExpenseModel.prototype.removeFromTags = function(tagsToRemove) {
    const currentTags = this.tags || [];
    const tagsToRemoveArray = Array.isArray(tagsToRemove) ? tagsToRemove : [tagsToRemove];
    this.tags = currentTags.filter(tag => !tagsToRemoveArray.includes(tag));
    return this.save();
  };

  ExpenseModel.prototype.getFormattedAmount = function() {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(this.amount);
  };

  ExpenseModel.prototype.getDaysAgo = function() {
    const today = new Date();
    const expenseDate = new Date(this.expenseDate);
    const diffTime = Math.abs(today - expenseDate);
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  // Class methods
  ExpenseModel.findByFarm = async function(farmId, options = {}) {
    return await this.findAll({
      where: { farmId },
      include: ['category', 'field', 'creator'],
      order: [['expenseDate', 'DESC']],
      ...options
    });
  };

  ExpenseModel.findByField = async function(fieldId, options = {}) {
    return await this.findAll({
      where: { fieldId },
      include: ['category', 'farm', 'creator'],
      order: [['expenseDate', 'DESC']],
      ...options
    });
  };

  ExpenseModel.findByCategory = async function(categoryId, options = {}) {
    return await this.findAll({
      where: { categoryId },
      include: ['farm', 'field', 'creator'],
      order: [['expenseDate', 'DESC']],
      ...options
    });
  };

  ExpenseModel.findByDateRange = async function(startDate, endDate, farmId = null) {
    const whereClause = {
      expenseDate: {
        [sequelize.Op.between]: [startDate, endDate]
      }
    };
    
    if (farmId) whereClause.farmId = farmId;
    
    return await this.findAll({
      where: whereClause,
      include: ['category', 'field', 'farm', 'creator'],
      order: [['expenseDate', 'DESC']]
    });
  };

  ExpenseModel.findRecurring = async function(farmId = null) {
    const whereClause = { isRecurring: true };
    if (farmId) whereClause.farmId = farmId;
    
    return await this.findAll({
      where: whereClause,
      include: ['category', 'field', 'farm'],
      order: [['expenseDate', 'DESC']]
    });
  };

  ExpenseModel.getTotalByCategory = async function(farmId, year = null) {
    let whereClause = { farmId };
    
    if (year) {
      whereClause.expenseDate = {
        [sequelize.Op.gte]: `${year}-01-01`,
        [sequelize.Op.lt]: `${year + 1}-01-01`
      };
    }
    
    return await this.findAll({
      where: whereClause,
      attributes: [
        'categoryId',
        [sequelize.fn('SUM', sequelize.col('amount')), 'totalAmount'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'expenseCount']
      ],
      include: [{
        model: sequelize.models.ExpenseCategory,
        as: 'category',
        attributes: ['name']
      }],
      group: ['categoryId', 'category.id'],
      order: [[sequelize.fn('SUM', sequelize.col('amount')), 'DESC']]
    });
  };

  ExpenseModel.getMonthlyTotals = async function(farmId, year = null) {
    const currentYear = year || new Date().getFullYear();
    
    return await this.findAll({
      where: {
        farmId,
        expenseDate: {
          [sequelize.Op.gte]: `${currentYear}-01-01`,
          [sequelize.Op.lt]: `${currentYear + 1}-01-01`
        }
      },
      attributes: [
        [sequelize.fn('EXTRACT', sequelize.literal('MONTH FROM expense_date')), 'month'],
        [sequelize.fn('SUM', sequelize.col('amount')), 'totalAmount'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'expenseCount']
      ],
      group: [sequelize.fn('EXTRACT', sequelize.literal('MONTH FROM expense_date'))],
      order: [[sequelize.fn('EXTRACT', sequelize.literal('MONTH FROM expense_date')), 'ASC']]
    });
  };

  // Associations
  ExpenseModel.associate = function(models) {
    // Expense belongs to Farm
    ExpenseModel.belongsTo(models.Farm, {
      foreignKey: 'farmId',
      as: 'farm'
    });

    // Expense belongs to Field (optional)
    ExpenseModel.belongsTo(models.Field, {
      foreignKey: 'fieldId',
      as: 'field'
    });

    // Expense belongs to ExpenseCategory
    ExpenseModel.belongsTo(models.ExpenseCategory, {
      foreignKey: 'categoryId',
      as: 'category'
    });

    // Expense belongs to User (creator)
    ExpenseModel.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator'
    });
  };

  return ExpenseModel;
};

export default Expense;
