import { DataTypes } from 'sequelize';

const UserSession = (sequelize) => {
  const UserSessionModel = sequelize.define('UserSession', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'user_id',
      references: {
        model: 'users',
        key: 'id'
      }
    },
    sessionToken: {
      type: DataTypes.STRING(255),
      allowNull: false,
      unique: true,
      field: 'session_token'
    },
    refreshToken: {
      type: DataTypes.STRING(255),
      allowNull: true,
      unique: true,
      field: 'refresh_token'
    },
    ipAddress: {
      type: DataTypes.INET,
      allowNull: true,
      field: 'ip_address'
    },
    userAgent: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'user_agent'
    },
    expiresAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'expires_at'
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      field: 'is_active'
    }
  }, {
    tableName: 'user_sessions',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['user_id']
      },
      {
        unique: true,
        fields: ['session_token']
      },
      {
        fields: ['expires_at']
      },
      {
        fields: ['is_active']
      }
    ]
  });

  // Instance methods
  UserSessionModel.prototype.isExpired = function() {
    return new Date() > this.expiresAt;
  };

  UserSessionModel.prototype.deactivate = async function() {
    this.isActive = false;
    await this.save();
  };

  // Class methods
  UserSessionModel.findActiveByToken = async function(token) {
    return await this.findOne({
      where: {
        sessionToken: token,
        isActive: true
      }
    });
  };

  UserSessionModel.findActiveByUser = async function(userId) {
    return await this.findAll({
      where: {
        userId,
        isActive: true
      },
      order: [['createdAt', 'DESC']]
    });
  };

  UserSessionModel.cleanupExpired = async function() {
    return await this.update(
      { isActive: false },
      {
        where: {
          expiresAt: {
            [sequelize.Op.lt]: new Date()
          },
          isActive: true
        }
      }
    );
  };

  // Associations
  UserSessionModel.associate = function(models) {
    UserSessionModel.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user'
    });
  };

  return UserSessionModel;
};

export default UserSession;
