import { DataTypes } from 'sequelize';

const Field = (sequelize) => {
  const FieldModel = sequelize.define('Field', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    farmId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'farm_id',
      references: {
        model: 'farms',
        key: 'id'
      }
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        notEmpty: {
          msg: 'Field name is required'
        },
        len: {
          args: [1, 255],
          msg: 'Field name must be between 1 and 255 characters'
        }
      }
    },
    acres: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      validate: {
        min: {
          args: 0.01,
          msg: 'Acres must be greater than 0'
        },
        isDecimal: {
          msg: 'Acres must be a valid decimal number'
        }
      }
    },
    soilType: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: 'soil_type',
      validate: {
        len: {
          args: [0, 100],
          msg: 'Soil type must be less than 100 characters'
        }
      }
    },
    drainageClass: {
      type: DataTypes.STRING(50),
      allowNull: true,
      field: 'drainage_class',
      validate: {
        len: {
          args: [0, 50],
          msg: 'Drainage class must be less than 50 characters'
        }
      }
    },
    slopePercentage: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
      field: 'slope_percentage',
      validate: {
        min: {
          args: 0,
          msg: 'Slope percentage must be positive'
        },
        max: {
          args: 100,
          msg: 'Slope percentage cannot exceed 100%'
        }
      }
    },
    coordinates: {
      type: DataTypes.JSONB,
      allowNull: true,
      validate: {
        isValidCoordinates(value) {
          if (value && typeof value === 'object') {
            if (value.type && value.coordinates) {
              // Basic GeoJSON validation
              if (value.type !== 'Polygon' && value.type !== 'Point') {
                throw new Error('Coordinates must be a valid GeoJSON Polygon or Point');
              }
            }
          }
        }
      }
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      field: 'is_active'
    }
  }, {
    tableName: 'fields',
    timestamps: true,
    paranoid: true, // Enable soft deletes
    underscored: true,
    indexes: [
      {
        fields: ['farm_id']
      },
      {
        fields: ['is_active']
      },
      {
        fields: ['name']
      }
    ]
  });

  // Instance methods
  FieldModel.prototype.getTotalExpenses = async function(year = null) {
    const whereClause = year ? 
      { fieldId: this.id, expenseDate: { [sequelize.Op.gte]: `${year}-01-01`, [sequelize.Op.lt]: `${year + 1}-01-01` } } :
      { fieldId: this.id };
    
    const expenses = await this.getExpenses({
      where: whereClause
    });
    
    return expenses.reduce((total, expense) => total + parseFloat(expense.amount || 0), 0);
  };

  FieldModel.prototype.getTotalRevenue = async function(year = null) {
    const whereClause = year ? 
      { fieldId: this.id, saleDate: { [sequelize.Op.gte]: `${year}-01-01`, [sequelize.Op.lt]: `${year + 1}-01-01` } } :
      { fieldId: this.id };
    
    const revenue = await this.getRevenueRecords({
      where: whereClause
    });
    
    return revenue.reduce((total, rev) => total + parseFloat(rev.amount || 0), 0);
  };

  FieldModel.prototype.getNetProfit = async function(year = null) {
    const totalRevenue = await this.getTotalRevenue(year);
    const totalExpenses = await this.getTotalExpenses(year);
    return totalRevenue - totalExpenses;
  };

  FieldModel.prototype.getProfitPerAcre = async function(year = null) {
    const netProfit = await this.getNetProfit(year);
    const acres = parseFloat(this.acres || 0);
    return acres > 0 ? netProfit / acres : 0;
  };

  FieldModel.prototype.getCurrentCrop = async function() {
    const currentYear = new Date().getFullYear();
    return await this.getCropPlans({
      where: { planYear: currentYear },
      limit: 1,
      order: [['createdAt', 'DESC']]
    });
  };

  FieldModel.prototype.getYieldHistory = async function(years = 5) {
    const endYear = new Date().getFullYear();
    const startYear = endYear - years;
    
    return await this.getCropPlans({
      where: {
        planYear: {
          [sequelize.Op.between]: [startYear, endYear]
        }
      },
      order: [['planYear', 'ASC']]
    });
  };

  FieldModel.prototype.getInputApplications = async function(year = null) {
    const whereClause = year ? 
      { fieldId: this.id, applicationDate: { [sequelize.Op.gte]: `${year}-01-01`, [sequelize.Op.lt]: `${year + 1}-01-01` } } :
      { fieldId: this.id };
    
    return await this.getInputApplications({
      where: whereClause,
      include: ['input']
    });
  };

  FieldModel.prototype.getArea = function() {
    return parseFloat(this.acres || 0);
  };

  FieldModel.prototype.isRentable = function() {
    return this.isActive && this.acres > 0;
  };

  // Class methods
  FieldModel.findByFarm = async function(farmId) {
    return await this.findAll({
      where: { farmId },
      order: [['name', 'ASC']]
    });
  };

  FieldModel.findActiveFields = async function(farmId = null) {
    const whereClause = { isActive: true };
    if (farmId) whereClause.farmId = farmId;
    
    return await this.findAll({
      where: whereClause,
      order: [['name', 'ASC']]
    });
  };

  FieldModel.findBySoilType = async function(soilType) {
    return await this.findAll({
      where: { soilType }
    });
  };

  FieldModel.findWithProfitability = async function(farmId, year = null) {
    const fields = await this.findByFarm(farmId);
    
    const fieldsWithMetrics = await Promise.all(
      fields.map(async (field) => {
        const metrics = {
          totalExpenses: await field.getTotalExpenses(year),
          totalRevenue: await field.getTotalRevenue(year),
          netProfit: await field.getNetProfit(year),
          profitPerAcre: await field.getProfitPerAcre(year)
        };
        
        return { ...field.toJSON(), metrics };
      })
    );
    
    return fieldsWithMetrics;
  };

  // Associations
  FieldModel.associate = function(models) {
    // Field belongs to Farm
    FieldModel.belongsTo(models.Farm, {
      foreignKey: 'farmId',
      as: 'farm'
    });

    // Field has many Expenses
    FieldModel.hasMany(models.Expense, {
      foreignKey: 'fieldId',
      as: 'expenses'
    });

    // Field has many Revenue records
    FieldModel.hasMany(models.Revenue, {
      foreignKey: 'fieldId',
      as: 'revenueRecords'
    });

    // Field has many Crop Plans
    FieldModel.hasMany(models.CropPlan, {
      foreignKey: 'fieldId',
      as: 'cropPlans'
    });

    // Field has many Input Applications
    FieldModel.hasMany(models.InputApplication, {
      foreignKey: 'fieldId',
      as: 'inputApplications'
    });

    // Field has many Equipment Usage records
    FieldModel.hasMany(models.EquipmentUsage, {
      foreignKey: 'fieldId',
      as: 'equipmentUsage'
    });

    // Field has many Grain Inventory records
    FieldModel.hasMany(models.GrainInventory, {
      foreignKey: 'fieldId',
      as: 'grainInventory'
    });
  };

  return FieldModel;
};

export default Field;
