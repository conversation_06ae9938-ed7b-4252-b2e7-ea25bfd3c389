import { DataTypes } from 'sequelize';

const Farm = (sequelize) => {
  const FarmModel = sequelize.define('Farm', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        notEmpty: {
          msg: 'Farm name is required'
        },
        len: {
          args: [1, 255],
          msg: 'Farm name must be between 1 and 255 characters'
        }
      }
    },
    ownerId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'owner_id',
      references: {
        model: 'users',
        key: 'id'
      }
    },
    address: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    city: {
      type: DataTypes.STRING(100),
      allowNull: true,
      validate: {
        len: {
          args: [0, 100],
          msg: 'City must be less than 100 characters'
        }
      }
    },
    state: {
      type: DataTypes.STRING(50),
      allowNull: true,
      validate: {
        len: {
          args: [0, 50],
          msg: 'State must be less than 50 characters'
        }
      }
    },
    zipCode: {
      type: DataTypes.STRING(20),
      allowNull: true,
      field: 'zip_code',
      validate: {
        is: {
          args: /^[0-9]{5}(-[0-9]{4})?$/,
          msg: 'ZIP code must be in format 12345 or 12345-6789'
        }
      }
    },
    country: {
      type: DataTypes.STRING(50),
      defaultValue: 'USA',
      validate: {
        len: {
          args: [0, 50],
          msg: 'Country must be less than 50 characters'
        }
      }
    },
    totalAcres: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      field: 'total_acres',
      validate: {
        min: {
          args: 0,
          msg: 'Total acres must be positive'
        }
      }
    },
    farmType: {
      type: DataTypes.STRING(50),
      allowNull: true,
      field: 'farm_type',
      validate: {
        len: {
          args: [0, 50],
          msg: 'Farm type must be less than 50 characters'
        }
      }
    },
    establishedYear: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'established_year',
      validate: {
        min: {
          args: 1800,
          msg: 'Established year must be after 1800'
        },
        max: {
          args: new Date().getFullYear(),
          msg: 'Established year cannot be in the future'
        }
      }
    },
    taxId: {
      type: DataTypes.STRING(50),
      allowNull: true,
      field: 'tax_id',
      validate: {
        len: {
          args: [0, 50],
          msg: 'Tax ID must be less than 50 characters'
        }
      }
    }
  }, {
    tableName: 'farms',
    timestamps: true,
    paranoid: true, // Enable soft deletes
    underscored: true,
    indexes: [
      {
        fields: ['owner_id']
      },
      {
        fields: ['name']
      },
      {
        fields: ['state']
      }
    ]
  });

  // Instance methods
  FarmModel.prototype.getTotalFieldAcres = async function() {
    const fields = await this.getFields();
    return fields.reduce((total, field) => total + parseFloat(field.acres || 0), 0);
  };

  FarmModel.prototype.getActiveFields = async function() {
    return await this.getFields({
      where: { isActive: true }
    });
  };

  FarmModel.prototype.getTotalExpenses = async function(year = null) {
    const whereClause = year ? 
      { farmId: this.id, expenseDate: { [sequelize.Op.gte]: `${year}-01-01`, [sequelize.Op.lt]: `${year + 1}-01-01` } } :
      { farmId: this.id };
    
    const expenses = await this.getExpenses({
      where: whereClause
    });
    
    return expenses.reduce((total, expense) => total + parseFloat(expense.amount || 0), 0);
  };

  FarmModel.prototype.getTotalRevenue = async function(year = null) {
    const whereClause = year ? 
      { farmId: this.id, saleDate: { [sequelize.Op.gte]: `${year}-01-01`, [sequelize.Op.lt]: `${year + 1}-01-01` } } :
      { farmId: this.id };
    
    const revenue = await this.getRevenueRecords({
      where: whereClause
    });
    
    return revenue.reduce((total, rev) => total + parseFloat(rev.amount || 0), 0);
  };

  FarmModel.prototype.getNetProfit = async function(year = null) {
    const totalRevenue = await this.getTotalRevenue(year);
    const totalExpenses = await this.getTotalExpenses(year);
    return totalRevenue - totalExpenses;
  };

  FarmModel.prototype.getProfitPerAcre = async function(year = null) {
    const netProfit = await this.getNetProfit(year);
    const totalAcres = parseFloat(this.totalAcres || 0);
    return totalAcres > 0 ? netProfit / totalAcres : 0;
  };

  FarmModel.prototype.getFullAddress = function() {
    const parts = [this.address, this.city, this.state, this.zipCode].filter(Boolean);
    return parts.join(', ');
  };

  // Class methods
  FarmModel.findByOwner = async function(ownerId) {
    return await this.findAll({
      where: { ownerId },
      include: ['fields', 'owner']
    });
  };

  FarmModel.findByState = async function(state) {
    return await this.findAll({
      where: { state }
    });
  };

  FarmModel.findWithMetrics = async function(farmId) {
    const farm = await this.findByPk(farmId, {
      include: ['owner', 'fields', 'expenses', 'revenueRecords']
    });
    
    if (!farm) return null;
    
    const metrics = {
      totalAcres: await farm.getTotalFieldAcres(),
      totalExpenses: await farm.getTotalExpenses(),
      totalRevenue: await farm.getTotalRevenue(),
      netProfit: await farm.getNetProfit(),
      profitPerAcre: await farm.getProfitPerAcre()
    };
    
    return { ...farm.toJSON(), metrics };
  };

  // Associations
  FarmModel.associate = function(models) {
    // Farm belongs to User (owner)
    FarmModel.belongsTo(models.User, {
      foreignKey: 'ownerId',
      as: 'owner'
    });

    // Farm has many Fields
    FarmModel.hasMany(models.Field, {
      foreignKey: 'farmId',
      as: 'fields'
    });

    // Farm has many Expenses
    FarmModel.hasMany(models.Expense, {
      foreignKey: 'farmId',
      as: 'expenses'
    });

    // Farm has many Revenue records
    FarmModel.hasMany(models.Revenue, {
      foreignKey: 'farmId',
      as: 'revenueRecords'
    });

    // Farm has many Equipment
    FarmModel.hasMany(models.Equipment, {
      foreignKey: 'farmId',
      as: 'equipment'
    });

    // Farm has many Inputs
    FarmModel.hasMany(models.Input, {
      foreignKey: 'farmId',
      as: 'inputs'
    });

    // Farm has many Grain Contracts
    FarmModel.hasMany(models.GrainContract, {
      foreignKey: 'farmId',
      as: 'grainContracts'
    });

    // Farm has many Crop Plans
    FarmModel.hasMany(models.CropPlan, {
      foreignKey: 'farmId',
      as: 'cropPlans'
    });

    // Farm has many Budgets
    FarmModel.hasMany(models.Budget, {
      foreignKey: 'farmId',
      as: 'budgets'
    });
  };

  return FarmModel;
};

export default Farm;
