import db from '../models/index.js';
import { generateTokenPair, refreshAccessToken, verifyToken } from '../utils/jwt.js';
import { hashPasswordWithSalt, comparePassword, validatePasswordStrength, generatePasswordResetToken, generateEmailVerificationToken } from '../utils/password.js';
import config from '../config/index.js';

const { User, UserSession, Farm } = db;

/**
 * Authentication Service
 * Handles all authentication-related business logic
 */
class AuthService {
  /**
   * Register a new user
   * @param {Object} userData - User registration data
   * @returns {Object} Registration result
   */
  async register(userData) {
    const { email, password, firstName, lastName, phone, farmName } = userData;

    // Check if user already exists
    const existingUser = await User.findByEmail(email);
    if (existingUser) {
      throw new Error('User with this email already exists');
    }

    // Validate password strength
    const passwordValidation = validatePasswordStrength(password);
    if (!passwordValidation.isValid) {
      throw new Error(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
    }

    // Hash password
    const { hash, salt } = await hashPasswordWithSalt(password);

    // Create user
    const user = await User.create({
      email: email.toLowerCase(),
      passwordHash: hash,
      salt,
      firstName,
      lastName,
      phone,
      role: 'user',
      emailVerified: false
    });

    // Create default farm if farmName provided
    let farm = null;
    if (farmName) {
      farm = await Farm.create({
        name: farmName,
        ownerId: user.id
      });
    }

    // Generate email verification token
    const emailVerificationToken = generateEmailVerificationToken();
    
    // TODO: Store verification token and send email
    // This would typically involve storing the token in a separate table
    // and sending an email to the user

    // Generate tokens
    const tokens = generateTokenPair(user);

    // Create session
    await this.createSession(user.id, tokens.accessToken, {
      ip: userData.ip,
      userAgent: userData.userAgent
    });

    return {
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        emailVerified: user.emailVerified,
        farm: farm ? { id: farm.id, name: farm.name } : null
      },
      tokens,
      emailVerificationToken
    };
  }

  /**
   * Login user
   * @param {Object} credentials - Login credentials
   * @returns {Object} Login result
   */
  async login(credentials) {
    const { email, password, ip, userAgent } = credentials;

    // Find user by email
    const user = await User.findOne({
      where: { email: email.toLowerCase() },
      include: [
        {
          model: Farm,
          as: 'farms',
          attributes: ['id', 'name']
        }
      ]
    });

    if (!user) {
      throw new Error('Invalid email or password');
    }

    // Check if user is active
    if (!user.isActive) {
      throw new Error('Account is deactivated. Please contact support.');
    }

    // Verify password
    const isPasswordValid = await comparePassword(password, user.passwordHash);
    if (!isPasswordValid) {
      throw new Error('Invalid email or password');
    }

    // Update last login
    await user.updateLastLogin();

    // Generate tokens
    const tokens = generateTokenPair(user);

    // Create session
    await this.createSession(user.id, tokens.accessToken, {
      ip,
      userAgent
    });

    return {
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        emailVerified: user.emailVerified,
        lastLogin: user.lastLogin,
        farms: user.farms
      },
      tokens
    };
  }

  /**
   * Logout user
   * @param {string} token - Access token
   * @param {string} userId - User ID
   * @returns {boolean} Success status
   */
  async logout(token, userId) {
    // Deactivate session
    await UserSession.update(
      { isActive: false },
      {
        where: {
          userId,
          sessionToken: token,
          isActive: true
        }
      }
    );

    // TODO: Add token to blacklist for additional security
    // This would involve storing blacklisted tokens in Redis or database

    return true;
  }

  /**
   * Logout from all devices
   * @param {string} userId - User ID
   * @returns {boolean} Success status
   */
  async logoutAll(userId) {
    // Deactivate all sessions
    await UserSession.update(
      { isActive: false },
      {
        where: {
          userId,
          isActive: true
        }
      }
    );

    return true;
  }

  /**
   * Refresh access token
   * @param {string} refreshToken - Refresh token
   * @returns {Object} New tokens
   */
  async refreshToken(refreshToken) {
    try {
      const decoded = verifyToken(refreshToken);
      
      if (decoded.type !== 'refresh') {
        throw new Error('Invalid refresh token type');
      }

      // Find user
      const user = await User.findByPk(decoded.id, {
        include: [
          {
            model: Farm,
            as: 'farms',
            attributes: ['id', 'name']
          }
        ]
      });

      if (!user || !user.isActive) {
        throw new Error('User not found or inactive');
      }

      // Generate new token pair
      const tokens = generateTokenPair(user);

      // Update session with new token
      await UserSession.update(
        { 
          sessionToken: tokens.accessToken,
          expiresAt: new Date(Date.now() + (7 * 24 * 60 * 60 * 1000)) // 7 days
        },
        {
          where: {
            userId: user.id,
            isActive: true
          }
        }
      );

      return {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
          farms: user.farms
        },
        tokens
      };
    } catch (error) {
      throw new Error(`Token refresh failed: ${error.message}`);
    }
  }

  /**
   * Change password
   * @param {string} userId - User ID
   * @param {string} currentPassword - Current password
   * @param {string} newPassword - New password
   * @returns {boolean} Success status
   */
  async changePassword(userId, currentPassword, newPassword) {
    const user = await User.findByPk(userId);
    if (!user) {
      throw new Error('User not found');
    }

    // Verify current password
    const isCurrentPasswordValid = await comparePassword(currentPassword, user.passwordHash);
    if (!isCurrentPasswordValid) {
      throw new Error('Current password is incorrect');
    }

    // Validate new password
    const passwordValidation = validatePasswordStrength(newPassword);
    if (!passwordValidation.isValid) {
      throw new Error(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
    }

    // Hash new password
    const { hash, salt } = await hashPasswordWithSalt(newPassword);

    // Update password
    await user.update({
      passwordHash: hash,
      salt
    });

    // Logout from all devices for security
    await this.logoutAll(userId);

    return true;
  }

  /**
   * Request password reset
   * @param {string} email - User email
   * @returns {string} Reset token
   */
  async requestPasswordReset(email) {
    const user = await User.findByEmail(email);
    if (!user) {
      // Don't reveal if email exists for security
      return 'If the email exists, a reset link has been sent';
    }

    const resetToken = generatePasswordResetToken();
    
    // TODO: Store reset token with expiration (typically in a separate table)
    // TODO: Send reset email
    
    return resetToken;
  }

  /**
   * Reset password with token
   * @param {string} token - Reset token
   * @param {string} newPassword - New password
   * @returns {boolean} Success status
   */
  async resetPassword(token, newPassword) {
    // TODO: Verify reset token from storage
    // This is a simplified implementation
    
    // Validate new password
    const passwordValidation = validatePasswordStrength(newPassword);
    if (!passwordValidation.isValid) {
      throw new Error(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
    }

    // TODO: Find user by reset token and update password
    // Hash new password and update user
    
    return true;
  }

  /**
   * Create user session
   * @param {string} userId - User ID
   * @param {string} token - Session token
   * @param {Object} metadata - Session metadata
   * @returns {Object} Session object
   */
  async createSession(userId, token, metadata = {}) {
    const expiresAt = new Date(Date.now() + (7 * 24 * 60 * 60 * 1000)); // 7 days

    return await UserSession.create({
      userId,
      sessionToken: token,
      ipAddress: metadata.ip,
      userAgent: metadata.userAgent,
      expiresAt,
      isActive: true
    });
  }

  /**
   * Get user sessions
   * @param {string} userId - User ID
   * @returns {Array} User sessions
   */
  async getUserSessions(userId) {
    return await UserSession.findAll({
      where: {
        userId,
        isActive: true
      },
      order: [['createdAt', 'DESC']]
    });
  }

  /**
   * Verify email
   * @param {string} token - Verification token
   * @returns {boolean} Success status
   */
  async verifyEmail(token) {
    // TODO: Implement email verification logic
    // Find user by verification token and mark email as verified
    return true;
  }

  /**
   * Get user profile
   * @param {string} userId - User ID
   * @returns {Object} User profile
   */
  async getUserProfile(userId) {
    const user = await User.findByPk(userId, {
      include: [
        {
          model: Farm,
          as: 'farms',
          attributes: ['id', 'name', 'totalAcres']
        }
      ]
    });

    if (!user) {
      throw new Error('User not found');
    }

    return user;
  }

  /**
   * Update user profile
   * @param {string} userId - User ID
   * @param {Object} updateData - Profile update data
   * @returns {Object} Updated user
   */
  async updateUserProfile(userId, updateData) {
    const user = await User.findByPk(userId);
    if (!user) {
      throw new Error('User not found');
    }

    const allowedFields = ['firstName', 'lastName', 'phone'];
    const filteredData = {};
    
    for (const field of allowedFields) {
      if (updateData[field] !== undefined) {
        filteredData[field] = updateData[field];
      }
    }

    await user.update(filteredData);
    return user;
  }
}

export default new AuthService();
