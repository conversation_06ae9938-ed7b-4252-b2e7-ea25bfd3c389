import express from 'express';
import helmet from 'helmet';
import cors from 'cors';
import compression from 'compression';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import config from './config/index.js';

// Import middleware
import corsMiddleware from './middleware/cors.js';
import validationMiddleware from './middleware/validation.js';
import rateLimitMiddleware from './middleware/rateLimit.js';
import errorHandler from './middleware/errorHandler.js';

// Import routes
import authRoutes from './routes/auth.js';
import indexRoutes from './routes/index.js';

// Import utilities
import logger from './utils/logger.js';
import { standardResponse } from './utils/response.js';

/**
 * Express Application Setup
 * Configures all middleware, routes, and error handling
 */
const createApp = () => {
  const app = express();

  // Trust proxy for accurate IP addresses (important for rate limiting)
  app.set('trust proxy', 1);

  // Security middleware - Helmet.js
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
        connectSrc: ["'self'"],
        fontSrc: ["'self'"],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        frameSrc: ["'none'"]
      }
    },
    crossOriginEmbedderPolicy: false,
    hsts: {
      maxAge: 31536000,
      includeSubDomains: true,
      preload: true
    }
  }));

  // CORS configuration
  app.use(corsMiddleware);

  // Compression middleware
  app.use(compression({
    level: 6,
    threshold: 1024,
    filter: (req, res) => {
      if (req.headers['x-no-compression']) {
        return false;
      }
      return compression.filter(req, res);
    }
  }));

  // Request logging
  if (config.NODE_ENV === 'development') {
    app.use(morgan('dev'));
  } else {
    app.use(morgan('combined', {
      stream: {
        write: (message) => logger.info(message.trim())
      }
    }));
  }

  // Body parsing middleware
  app.use(express.json({ 
    limit: '10mb',
    strict: true
  }));
  app.use(express.urlencoded({ 
    extended: true, 
    limit: '10mb' 
  }));

  // Global rate limiting
  app.use(rateLimitMiddleware.global);

  // Request ID middleware for tracking
  app.use((req, res, next) => {
    req.id = Math.random().toString(36).substr(2, 9);
    res.setHeader('X-Request-ID', req.id);
    next();
  });

  // Request timing middleware
  app.use((req, res, next) => {
    req.startTime = Date.now();
    next();
  });

  // Health check endpoint (before other routes)
  app.get('/health', (req, res) => {
    res.json(standardResponse(true, 'Server is healthy', {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: config.NODE_ENV,
      version: process.env.npm_package_version || '1.0.0'
    }));
  });

  // API status endpoint
  app.get('/api/status', (req, res) => {
    res.json(standardResponse(true, 'API is running', {
      status: 'operational',
      version: config.API_VERSION,
      timestamp: new Date().toISOString(),
      environment: config.NODE_ENV
    }));
  });

  // API routes
  app.use('/api/auth', authRoutes);
  app.use('/api', indexRoutes);

  // 404 handler for undefined routes
  app.use('*', (req, res) => {
    res.status(404).json(standardResponse(false, 'Route not found', null, 'ROUTE_NOT_FOUND'));
  });

  // Global error handling middleware (must be last)
  app.use(errorHandler);

  // Response time logging
  app.use((req, res, next) => {
    const responseTime = Date.now() - req.startTime;
    logger.info(`${req.method} ${req.originalUrl} - ${res.statusCode} - ${responseTime}ms`);
    next();
  });

  return app;
};

export default createApp;
