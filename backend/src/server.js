import createApp from './app.js';
import config from './config/index.js';
import db from './models/index.js';
import logger from './utils/logger.js';

/**
 * Server Initialization
 * Starts the Express server and initializes database connections
 */

const startServer = async () => {
  try {
    // Test database connection
    logger.info('🔗 Testing database connection...');
    await db.testConnection();

    // Sync database in development
    if (config.NODE_ENV === 'development') {
      logger.info('🔄 Synchronizing database...');
      await db.syncDatabase(false); // Don't force recreate tables
    }

    // Create Express app
    const app = createApp();

    // Start server
    const server = app.listen(config.PORT, '0.0.0.0', () => {
      logger.info(`🚀 Server running on port ${config.PORT}`);
      logger.info(`📊 Environment: ${config.NODE_ENV}`);
      logger.info(`🌐 API Version: ${config.API_VERSION}`);
      logger.info(`🔗 Database: Connected`);
      
      if (config.NODE_ENV === 'development') {
        logger.info(`🔧 Health Check: http://localhost:${config.PORT}/health`);
        logger.info(`📡 API Status: http://localhost:${config.PORT}/api/status`);
        logger.info(`🔐 Auth Endpoints: http://localhost:${config.PORT}/api/auth`);
      }
    });

    // Graceful shutdown handling
    const gracefulShutdown = async (signal) => {
      logger.info(`\n🛑 Received ${signal}. Starting graceful shutdown...`);
      
      // Stop accepting new connections
      server.close(async () => {
        logger.info('📡 HTTP server closed');
        
        try {
          // Close database connections
          await db.closeConnection();
          logger.info('🔗 Database connections closed');
          
          logger.info('✅ Graceful shutdown completed');
          process.exit(0);
        } catch (error) {
          logger.error('❌ Error during shutdown:', error);
          process.exit(1);
        }
      });

      // Force shutdown after 30 seconds
      setTimeout(() => {
        logger.error('⚠️  Forced shutdown after 30 seconds');
        process.exit(1);
      }, 30000);
    };

    // Handle shutdown signals
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('💥 Uncaught Exception:', error);
      gracefulShutdown('UNCAUGHT_EXCEPTION');
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('💥 Unhandled Rejection at:', promise, 'reason:', reason);
      gracefulShutdown('UNHANDLED_REJECTION');
    });

    return server;

  } catch (error) {
    logger.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

// Start the server if this file is run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  startServer();
}

export default startServer;
