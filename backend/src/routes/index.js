import express from 'express';
import { standardResponse } from '../utils/response.js';
import config from '../config/index.js';

const router = express.Router();

/**
 * Main API Routes
 * Aggregates all API route modules
 */

/**
 * @route   GET /api
 * @desc    API root endpoint
 * @access  Public
 */
router.get('/', (req, res) => {
  res.json(standardResponse(true, 'Harvest Profit Pro API', {
    version: config.API_VERSION,
    environment: config.NODE_ENV,
    timestamp: new Date().toISOString(),
    endpoints: {
      auth: '/api/auth',
      health: '/health',
      status: '/api/status'
    },
    documentation: {
      swagger: '/api/docs',
      postman: '/api/postman'
    }
  }));
});

/**
 * @route   GET /api/version
 * @desc    Get API version information
 * @access  Public
 */
router.get('/version', (req, res) => {
  res.json(standardResponse(true, 'API version information', {
    version: config.API_VERSION,
    buildDate: new Date().toISOString(),
    nodeVersion: process.version,
    environment: config.NODE_ENV
  }));
});

/**
 * @route   GET /api/ping
 * @desc    Simple ping endpoint for connectivity testing
 * @access  Public
 */
router.get('/ping', (req, res) => {
  res.json(standardResponse(true, 'pong', {
    timestamp: new Date().toISOString(),
    requestId: req.id
  }));
});

// Future route imports will go here
// Example:
// import farmRoutes from './farms.js';
// import expenseRoutes from './expenses.js';
// import dashboardRoutes from './dashboard.js';
// 
// router.use('/farms', farmRoutes);
// router.use('/expenses', expenseRoutes);
// router.use('/dashboard', dashboardRoutes);

export default router;
