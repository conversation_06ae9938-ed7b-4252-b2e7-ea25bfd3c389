# Harvest Profit Pro - Backend API

## Phase 1: Backend Foundation ✅ COMPLETE

A comprehensive farm finance management system backend built with Node.js, Express, and PostgreSQL.

## 🏗️ Architecture Overview

### Technology Stack
- **Runtime**: Node.js 18+
- **Framework**: Express.js 4.18+
- **Database**: PostgreSQL with Sequelize ORM
- **Authentication**: JWT with bcrypt password hashing
- **Security**: Helmet.js, CORS, Rate Limiting, Input Sanitization
- **Logging**: <PERSON> with daily log rotation
- **Testing**: Jest with Supertest
- **Containerization**: Docker

### Project Structure
```
backend/
├── src/
│   ├── config/           # Configuration files
│   ├── controllers/      # Request handlers
│   ├── middleware/       # Express middleware
│   ├── models/          # Sequelize models
│   ├── routes/          # API routes
│   ├── services/        # Business logic
│   └── utils/           # Utility functions
├── database/
│   ├── migrations/      # Database migrations
│   ├── seeds/          # Seed data
│   └── init/           # Database initialization
├── tests/              # Test files
└── scripts/            # Utility scripts
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- PostgreSQL 12+
- npm 9+

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd harvest-profit-pro/backend

# Install dependencies
npm install

# Copy environment template
cp .env.example .env

# Configure your environment variables
nano .env
```

### Environment Configuration
```env
# Application
NODE_ENV=development
PORT=5000
API_VERSION=v1

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/harvest_profit_pro
DB_HOST=localhost
DB_PORT=5432
DB_NAME=harvest_profit_pro
DB_USER=harvest_user
DB_PASSWORD=your_password

# JWT
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# Security
BCRYPT_ROUNDS=12
CORS_ORIGIN=http://localhost:3000
```

### Database Setup
```bash
# Create database
npm run db:create

# Run migrations
npm run db:migrate

# Seed initial data
npm run db:seed
```

### Running the Application
```bash
# Development mode
npm run dev

# Production mode
npm start

# Run tests
npm test

# Run with coverage
npm run test:coverage
```

## 📊 Database Schema

### Core Tables
- **users**: User authentication and profiles
- **farms**: Farm management and ownership
- **fields**: Field information and boundaries
- **crops**: Crop types and characteristics
- **expense_categories**: Expense categorization
- **expenses**: Expense tracking and management
- **revenue**: Income and sales tracking
- **budgets**: Budget planning and monitoring

### Additional Tables
- **input_categories**: Input type classification
- **inputs**: Input inventory management
- **input_applications**: Field input applications
- **equipment**: Equipment tracking
- **equipment_maintenance**: Maintenance records
- **equipment_usage**: Equipment utilization
- **grain_contracts**: Contract management
- **market_prices**: Commodity price tracking
- **grain_inventory**: Inventory management
- **crop_plans**: Crop planning and scheduling
- **user_sessions**: Session management
- **notifications**: User notifications
- **audit_logs**: System audit trail

## 🔐 Authentication & Security

### Authentication Flow
1. User registration with email verification
2. JWT-based authentication with refresh tokens
3. Role-based access control (admin, manager, user)
4. Session management with automatic cleanup

### Security Features
- **Input Sanitization**: XSS and injection prevention
- **Rate Limiting**: Configurable rate limits per endpoint
- **CORS Protection**: Configurable cross-origin policies
- **Security Headers**: Helmet.js security headers
- **Password Security**: bcrypt with configurable rounds
- **SQL Injection Prevention**: Parameterized queries
- **CSRF Protection**: Token-based CSRF protection

## 📝 API Documentation

### Authentication Endpoints
```
POST /api/auth/register     # User registration
POST /api/auth/login        # User login
POST /api/auth/logout       # User logout
POST /api/auth/refresh      # Token refresh
PUT  /api/auth/change-password  # Password change
GET  /api/auth/profile      # Get user profile
PUT  /api/auth/profile      # Update user profile
```

### Health Check Endpoints
```
GET  /health               # Health check
GET  /api/status          # API status
GET  /api/ping            # Connectivity test
```

## 🧪 Testing

### Test Structure
```
tests/
├── unit/                 # Unit tests
├── integration/          # Integration tests
├── fixtures/            # Test data
└── setup.js            # Test configuration
```

### Running Tests
```bash
# All tests
npm test

# Unit tests only
npm run test:unit

# Integration tests only
npm run test:integration

# Watch mode
npm run test:watch

# Coverage report
npm run test:coverage
```

## 📊 Logging & Monitoring

### Log Levels
- **error**: Application errors and exceptions
- **warn**: Warning conditions and security events
- **info**: General application information
- **http**: HTTP request/response logging
- **debug**: Detailed debugging information

### Log Files
```
logs/
├── all-YYYY-MM-DD.log        # All log levels
├── error-YYYY-MM-DD.log      # Error logs only
├── http-YYYY-MM-DD.log       # HTTP request logs
├── security-YYYY-MM-DD.log   # Security events
└── performance-YYYY-MM-DD.log # Performance metrics
```

## 🐳 Docker Support

### Development
```bash
# Build development image
docker build -t harvest-profit-pro-backend:dev .

# Run with docker-compose
docker-compose up -d
```

### Production
```bash
# Build production image
docker build --target production -t harvest-profit-pro-backend:prod .

# Run production container
docker run -p 5000:5000 harvest-profit-pro-backend:prod
```

## 🔧 Development Tools

### Code Quality
```bash
# Linting
npm run lint
npm run lint:fix

# Code formatting
npm run format

# Type checking
npm run type-check
```

### Database Management
```bash
# Create migration
npx sequelize-cli migration:generate --name migration-name

# Create seed
npx sequelize-cli seed:generate --name seed-name

# Reset database
npm run db:reset
```

## 📈 Performance Considerations

### Optimizations Implemented
- Database indexing on frequently queried columns
- Connection pooling for database connections
- Request compression with gzip
- Rate limiting to prevent abuse
- Efficient logging with log rotation
- Memory-efficient error handling

### Monitoring
- Request/response time logging
- Database query performance tracking
- Memory usage monitoring
- Error rate tracking

## 🚀 Phase 2 Readiness

Phase 1 provides a solid foundation for Phase 2 implementation:

✅ **Complete Database Schema**: All 11 feature tables implemented
✅ **Authentication System**: JWT-based auth with role management
✅ **Security Framework**: Comprehensive security measures
✅ **Error Handling**: Robust error handling and logging
✅ **Testing Infrastructure**: Unit and integration test setup
✅ **API Infrastructure**: Express server with middleware stack

### Next Steps for Phase 2
1. Implement CRUD APIs for all entities
2. Add business logic for farm operations
3. Implement data validation and business rules
4. Add advanced querying and filtering
5. Implement real-time features with WebSockets

## 📞 Support

For questions or issues:
1. Check the logs in the `logs/` directory
2. Run the validation script: `node scripts/validate-phase1.js`
3. Review the test output: `npm test`
4. Check the API health: `GET /health`

## 📄 License

MIT License - see LICENSE file for details.
