/**
 * Jest Test Setup
 * Global test configuration and setup
 */

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-only';
process.env.BCRYPT_ROUNDS = '4'; // Lower rounds for faster tests
process.env.DB_NAME = 'harvest_profit_pro_test';
process.env.LOG_LEVEL = 'error'; // Reduce log noise during tests

// Global test timeout
jest.setTimeout(30000);

// Mock console methods to reduce noise during tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
};

// Global test utilities
global.testUtils = {
  createMockUser: () => ({
    id: 'test-user-id',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    role: 'user',
    isActive: true,
    emailVerified: true
  }),
  
  createMockFarm: () => ({
    id: 'test-farm-id',
    name: 'Test Farm',
    ownerId: 'test-user-id',
    totalAcres: 100.0,
    city: 'Test City',
    state: 'Test State'
  }),
  
  createMockField: () => ({
    id: 'test-field-id',
    farmId: 'test-farm-id',
    name: 'Test Field',
    acres: 25.0,
    soilType: 'Loam',
    isActive: true
  })
};

// Setup and teardown hooks
beforeAll(async () => {
  // Global setup before all tests
});

afterAll(async () => {
  // Global cleanup after all tests
});

beforeEach(() => {
  // Setup before each test
  jest.clearAllMocks();
});

afterEach(() => {
  // Cleanup after each test
});
