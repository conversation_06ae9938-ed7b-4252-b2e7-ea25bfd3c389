import { jest } from '@jest/globals';
import { generateTokenPair, verifyToken, extractTokenFromHeader } from '../../src/utils/jwt.js';
import { hashPasswordWithSalt, comparePassword, validatePasswordStrength } from '../../src/utils/password.js';
import { sanitizeInput, sanitizeObject } from '../../src/utils/sanitization.js';

/**
 * Unit Tests for Phase 1 Authentication Components
 */

describe('JWT Utilities', () => {
  const mockUser = {
    id: 'test-user-id',
    email: '<EMAIL>',
    role: 'user'
  };

  test('should generate valid token pair', () => {
    const tokens = generateTokenPair(mockUser);
    
    expect(tokens).toHaveProperty('accessToken');
    expect(tokens).toHaveProperty('refreshToken');
    expect(tokens).toHaveProperty('expiresIn');
    expect(tokens).toHaveProperty('tokenType', 'Bearer');
    expect(typeof tokens.accessToken).toBe('string');
    expect(typeof tokens.refreshToken).toBe('string');
  });

  test('should verify valid token', () => {
    const tokens = generateTokenPair(mockUser);
    const decoded = verifyToken(tokens.accessToken);
    
    expect(decoded).toHaveProperty('id', mockUser.id);
    expect(decoded).toHaveProperty('email', mockUser.email);
    expect(decoded).toHaveProperty('role', mockUser.role);
  });

  test('should extract token from authorization header', () => {
    const token = 'test-token-123';
    const authHeader = `Bearer ${token}`;
    
    const extracted = extractTokenFromHeader(authHeader);
    expect(extracted).toBe(token);
  });

  test('should return null for invalid authorization header', () => {
    expect(extractTokenFromHeader('Invalid header')).toBeNull();
    expect(extractTokenFromHeader('')).toBeNull();
    expect(extractTokenFromHeader(null)).toBeNull();
  });
});

describe('Password Utilities', () => {
  const testPassword = 'TestPassword123!';
  const weakPassword = '123';

  test('should hash password with salt', async () => {
    const result = await hashPasswordWithSalt(testPassword);
    
    expect(result).toHaveProperty('hash');
    expect(result).toHaveProperty('salt');
    expect(typeof result.hash).toBe('string');
    expect(typeof result.salt).toBe('string');
    expect(result.hash).not.toBe(testPassword);
  });

  test('should compare password correctly', async () => {
    const { hash } = await hashPasswordWithSalt(testPassword);
    
    const isValid = await comparePassword(testPassword, hash);
    const isInvalid = await comparePassword('wrongpassword', hash);
    
    expect(isValid).toBe(true);
    expect(isInvalid).toBe(false);
  });

  test('should validate strong password', () => {
    const validation = validatePasswordStrength(testPassword);
    
    expect(validation.isValid).toBe(true);
    expect(validation.errors).toHaveLength(0);
    expect(validation.score).toBeGreaterThan(3);
  });

  test('should reject weak password', () => {
    const validation = validatePasswordStrength(weakPassword);
    
    expect(validation.isValid).toBe(false);
    expect(validation.errors.length).toBeGreaterThan(0);
    expect(validation.score).toBeLessThan(3);
  });
});

describe('Input Sanitization', () => {
  test('should sanitize HTML input', () => {
    const maliciousInput = '<script>alert("xss")</script>Hello';
    const sanitized = sanitizeInput(maliciousInput);
    
    expect(sanitized).not.toContain('<script>');
    expect(sanitized).not.toContain('alert');
    expect(sanitized).toContain('Hello');
  });

  test('should sanitize object recursively', () => {
    const maliciousObject = {
      name: '<script>alert("xss")</script>John',
      email: '<EMAIL>',
      nested: {
        value: '<img src="x" onerror="alert(1)">'
      }
    };
    
    const sanitized = sanitizeObject(maliciousObject);
    
    expect(sanitized.name).not.toContain('<script>');
    expect(sanitized.email).toBe('<EMAIL>');
    expect(sanitized.nested.value).not.toContain('onerror');
  });

  test('should handle null and undefined values', () => {
    expect(sanitizeInput(null)).toBeNull();
    expect(sanitizeInput(undefined)).toBeUndefined();
    expect(sanitizeObject(null)).toBeNull();
  });

  test('should preserve safe content', () => {
    const safeInput = 'This is safe content 123';
    const sanitized = sanitizeInput(safeInput);
    
    expect(sanitized).toBe(safeInput);
  });
});

describe('Error Handling', () => {
  test('should handle validation errors properly', () => {
    // This would test custom error classes
    // Implementation depends on your specific error handling setup
    expect(true).toBe(true); // Placeholder
  });
});

describe('Security Middleware', () => {
  test('should detect SQL injection attempts', () => {
    // This would test SQL injection detection
    // Implementation depends on your specific security middleware
    expect(true).toBe(true); // Placeholder
  });

  test('should detect XSS attempts', () => {
    // This would test XSS detection
    // Implementation depends on your specific security middleware
    expect(true).toBe(true); // Placeholder
  });
});
