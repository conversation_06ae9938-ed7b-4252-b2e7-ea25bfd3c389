-- Harvest Profit Pro Database Schema
-- Complete database schema for farm finance management system

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Core Tables

-- Users table for authentication and user management
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    salt VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VA<PERSON>HAR(100) NOT NULL,
    phone VARCHAR(20),
    role VARCHAR(20) DEFAULT 'user' CHECK (role IN ('admin', 'manager', 'user')),
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Farms table for farm management
CREATE TABLE farms (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    owner_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(50),
    zip_code VARCHAR(20),
    country VARCHAR(50) DEFAULT 'USA',
    total_acres DECIMAL(10,2),
    farm_type VARCHAR(50),
    established_year INTEGER,
    tax_id VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Fields table for field management
CREATE TABLE fields (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    farm_id UUID NOT NULL REFERENCES farms(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    acres DECIMAL(10,2) NOT NULL,
    soil_type VARCHAR(100),
    drainage_class VARCHAR(50),
    slope_percentage DECIMAL(5,2),
    coordinates JSONB, -- Store field boundary coordinates
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Crops table for crop types
CREATE TABLE crops (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    category VARCHAR(50), -- grain, vegetable, fruit, etc.
    typical_yield_per_acre DECIMAL(10,2),
    typical_price_per_unit DECIMAL(10,2),
    unit_of_measure VARCHAR(20), -- bushels, tons, pounds, etc.
    growing_season_days INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Financial Tables

-- Expense categories for organizing expenses
CREATE TABLE expense_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    parent_category_id UUID REFERENCES expense_categories(id),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Expenses table for expense tracking
CREATE TABLE expenses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    farm_id UUID NOT NULL REFERENCES farms(id) ON DELETE CASCADE,
    field_id UUID REFERENCES fields(id) ON DELETE SET NULL,
    category_id UUID NOT NULL REFERENCES expense_categories(id),
    amount DECIMAL(12,2) NOT NULL,
    description TEXT,
    expense_date DATE NOT NULL,
    vendor VARCHAR(255),
    receipt_url VARCHAR(500),
    payment_method VARCHAR(50),
    is_recurring BOOLEAN DEFAULT false,
    recurring_frequency VARCHAR(20), -- monthly, quarterly, annually
    tags JSONB,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Revenue table for income tracking
CREATE TABLE revenue (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    farm_id UUID NOT NULL REFERENCES farms(id) ON DELETE CASCADE,
    field_id UUID REFERENCES fields(id) ON DELETE SET NULL,
    crop_id UUID REFERENCES crops(id) ON DELETE SET NULL,
    amount DECIMAL(12,2) NOT NULL,
    quantity DECIMAL(10,2),
    price_per_unit DECIMAL(10,2),
    sale_date DATE NOT NULL,
    buyer VARCHAR(255),
    contract_number VARCHAR(100),
    payment_terms VARCHAR(100),
    payment_received BOOLEAN DEFAULT false,
    payment_date DATE,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Budgets table for budget planning
CREATE TABLE budgets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    farm_id UUID NOT NULL REFERENCES farms(id) ON DELETE CASCADE,
    field_id UUID REFERENCES fields(id) ON DELETE SET NULL,
    category_id UUID REFERENCES expense_categories(id),
    budget_year INTEGER NOT NULL,
    budgeted_amount DECIMAL(12,2) NOT NULL,
    actual_amount DECIMAL(12,2) DEFAULT 0,
    variance DECIMAL(12,2) GENERATED ALWAYS AS (actual_amount - budgeted_amount) STORED,
    notes TEXT,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Input Management Tables

-- Input categories for organizing inputs
CREATE TABLE input_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    unit_of_measure VARCHAR(20), -- gallons, pounds, tons, etc.
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Inputs table for input inventory
CREATE TABLE inputs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    farm_id UUID NOT NULL REFERENCES farms(id) ON DELETE CASCADE,
    category_id UUID NOT NULL REFERENCES input_categories(id),
    name VARCHAR(255) NOT NULL,
    brand VARCHAR(100),
    active_ingredient VARCHAR(255),
    concentration DECIMAL(5,2),
    unit_cost DECIMAL(10,2) NOT NULL,
    quantity_on_hand DECIMAL(10,2) DEFAULT 0,
    reorder_level DECIMAL(10,2),
    supplier VARCHAR(255),
    purchase_date DATE,
    expiration_date DATE,
    lot_number VARCHAR(100),
    storage_location VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Input applications for tracking usage
CREATE TABLE input_applications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    farm_id UUID NOT NULL REFERENCES farms(id) ON DELETE CASCADE,
    field_id UUID NOT NULL REFERENCES fields(id) ON DELETE CASCADE,
    input_id UUID NOT NULL REFERENCES inputs(id) ON DELETE CASCADE,
    application_date DATE NOT NULL,
    quantity_applied DECIMAL(10,2) NOT NULL,
    application_rate DECIMAL(10,2), -- per acre
    method VARCHAR(100), -- spray, broadcast, etc.
    weather_conditions TEXT,
    operator VARCHAR(255),
    equipment_used VARCHAR(255),
    cost DECIMAL(10,2),
    notes TEXT,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Equipment Management Tables

-- Equipment table for equipment tracking
CREATE TABLE equipment (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    farm_id UUID NOT NULL REFERENCES farms(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(100), -- tractor, combine, planter, etc.
    make VARCHAR(100),
    model VARCHAR(100),
    year INTEGER,
    serial_number VARCHAR(100),
    purchase_date DATE,
    purchase_price DECIMAL(12,2),
    current_value DECIMAL(12,2),
    depreciation_method VARCHAR(50),
    useful_life_years INTEGER,
    hours_meter_reading DECIMAL(10,1) DEFAULT 0,
    fuel_type VARCHAR(50),
    insurance_policy VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Equipment maintenance for tracking maintenance
CREATE TABLE equipment_maintenance (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    equipment_id UUID NOT NULL REFERENCES equipment(id) ON DELETE CASCADE,
    maintenance_type VARCHAR(100) NOT NULL, -- routine, repair, inspection
    description TEXT NOT NULL,
    maintenance_date DATE NOT NULL,
    hours_at_maintenance DECIMAL(10,1),
    cost DECIMAL(10,2),
    vendor VARCHAR(255),
    parts_replaced TEXT,
    next_maintenance_date DATE,
    next_maintenance_hours DECIMAL(10,1),
    performed_by VARCHAR(255),
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Equipment usage for tracking utilization
CREATE TABLE equipment_usage (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    equipment_id UUID NOT NULL REFERENCES equipment(id) ON DELETE CASCADE,
    field_id UUID REFERENCES fields(id) ON DELETE SET NULL,
    usage_date DATE NOT NULL,
    hours_used DECIMAL(10,1) NOT NULL,
    fuel_consumed DECIMAL(10,2),
    operation_type VARCHAR(100), -- planting, harvesting, tillage, etc.
    operator VARCHAR(255),
    notes TEXT,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Grain Marketing Tables

-- Grain contracts for contract management
CREATE TABLE grain_contracts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    farm_id UUID NOT NULL REFERENCES farms(id) ON DELETE CASCADE,
    crop_id UUID NOT NULL REFERENCES crops(id) ON DELETE CASCADE,
    contract_number VARCHAR(100) UNIQUE NOT NULL,
    buyer VARCHAR(255) NOT NULL,
    quantity DECIMAL(10,2) NOT NULL,
    price_per_unit DECIMAL(10,2) NOT NULL,
    total_value DECIMAL(12,2) GENERATED ALWAYS AS (quantity * price_per_unit) STORED,
    contract_date DATE NOT NULL,
    delivery_start_date DATE,
    delivery_end_date DATE,
    delivery_location VARCHAR(255),
    contract_type VARCHAR(50), -- cash, forward, basis
    status VARCHAR(50) DEFAULT 'active', -- active, delivered, cancelled
    quality_specifications JSONB,
    payment_terms VARCHAR(255),
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Market prices for commodity tracking
CREATE TABLE market_prices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    crop_id UUID NOT NULL REFERENCES crops(id) ON DELETE CASCADE,
    price DECIMAL(10,2) NOT NULL,
    price_date DATE NOT NULL,
    market_location VARCHAR(255),
    price_type VARCHAR(50), -- cash, futures, basis
    source VARCHAR(100), -- exchange, elevator, etc.
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(crop_id, price_date, market_location, price_type)
);

-- Grain inventory for inventory tracking
CREATE TABLE grain_inventory (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    farm_id UUID NOT NULL REFERENCES farms(id) ON DELETE CASCADE,
    crop_id UUID NOT NULL REFERENCES crops(id) ON DELETE CASCADE,
    field_id UUID REFERENCES fields(id) ON DELETE SET NULL,
    quantity DECIMAL(10,2) NOT NULL,
    storage_location VARCHAR(255),
    harvest_date DATE,
    moisture_content DECIMAL(5,2),
    test_weight DECIMAL(5,2),
    quality_grade VARCHAR(50),
    storage_cost_per_unit DECIMAL(10,4),
    is_sold BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Crop Planning Tables

-- Crop plans for planning activities
CREATE TABLE crop_plans (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    farm_id UUID NOT NULL REFERENCES farms(id) ON DELETE CASCADE,
    field_id UUID NOT NULL REFERENCES fields(id) ON DELETE CASCADE,
    crop_id UUID NOT NULL REFERENCES crops(id) ON DELETE CASCADE,
    plan_year INTEGER NOT NULL,
    planned_acres DECIMAL(10,2) NOT NULL,
    expected_yield DECIMAL(10,2),
    expected_price DECIMAL(10,2),
    projected_revenue DECIMAL(12,2),
    projected_costs DECIMAL(12,2),
    projected_profit DECIMAL(12,2) GENERATED ALWAYS AS (projected_revenue - projected_costs) STORED,
    planting_date DATE,
    harvest_date DATE,
    notes TEXT,
    status VARCHAR(50) DEFAULT 'planned', -- planned, planted, harvested
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Planting schedules for timing management
CREATE TABLE planting_schedules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    crop_plan_id UUID NOT NULL REFERENCES crop_plans(id) ON DELETE CASCADE,
    activity_name VARCHAR(255) NOT NULL,
    scheduled_date DATE NOT NULL,
    actual_date DATE,
    status VARCHAR(50) DEFAULT 'scheduled', -- scheduled, completed, cancelled
    notes TEXT,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Harvest schedules for harvest planning
CREATE TABLE harvest_schedules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    crop_plan_id UUID NOT NULL REFERENCES crop_plans(id) ON DELETE CASCADE,
    scheduled_start_date DATE NOT NULL,
    scheduled_end_date DATE,
    actual_start_date DATE,
    actual_end_date DATE,
    actual_yield DECIMAL(10,2),
    actual_quality JSONB,
    status VARCHAR(50) DEFAULT 'scheduled', -- scheduled, in_progress, completed
    notes TEXT,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Working Capital Management Tables

-- Capital transactions for cash flow tracking
CREATE TABLE capital_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    farm_id UUID NOT NULL REFERENCES farms(id) ON DELETE CASCADE,
    transaction_type VARCHAR(50) NOT NULL, -- income, expense, loan, investment
    amount DECIMAL(12,2) NOT NULL,
    description TEXT NOT NULL,
    transaction_date DATE NOT NULL,
    category VARCHAR(100),
    account VARCHAR(100),
    reference_number VARCHAR(100),
    is_recurring BOOLEAN DEFAULT false,
    recurring_frequency VARCHAR(20),
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Capital budgets for working capital planning
CREATE TABLE capital_budgets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    farm_id UUID NOT NULL REFERENCES farms(id) ON DELETE CASCADE,
    budget_year INTEGER NOT NULL,
    category VARCHAR(100) NOT NULL,
    budgeted_amount DECIMAL(12,2) NOT NULL,
    actual_amount DECIMAL(12,2) DEFAULT 0,
    variance DECIMAL(12,2) GENERATED ALWAYS AS (actual_amount - budgeted_amount) STORED,
    alert_threshold DECIMAL(12,2),
    notes TEXT,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- System Tables

-- User sessions for session management
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    refresh_token VARCHAR(255) UNIQUE,
    ip_address INET,
    user_agent TEXT,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Notifications for user alerts
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type VARCHAR(50) DEFAULT 'info', -- info, warning, error, success
    is_read BOOLEAN DEFAULT false,
    action_url VARCHAR(500),
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Audit logs for system tracking
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(100),
    record_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for Performance Optimization

-- Users indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_active ON users(is_active);

-- Farms indexes
CREATE INDEX idx_farms_owner ON farms(owner_id);
CREATE INDEX idx_farms_name ON farms(name);

-- Fields indexes
CREATE INDEX idx_fields_farm ON fields(farm_id);
CREATE INDEX idx_fields_active ON fields(is_active);

-- Expenses indexes
CREATE INDEX idx_expenses_farm ON expenses(farm_id);
CREATE INDEX idx_expenses_field ON expenses(field_id);
CREATE INDEX idx_expenses_category ON expenses(category_id);
CREATE INDEX idx_expenses_date ON expenses(expense_date);
CREATE INDEX idx_expenses_amount ON expenses(amount);

-- Revenue indexes
CREATE INDEX idx_revenue_farm ON revenue(farm_id);
CREATE INDEX idx_revenue_field ON revenue(field_id);
CREATE INDEX idx_revenue_crop ON revenue(crop_id);
CREATE INDEX idx_revenue_date ON revenue(sale_date);

-- Equipment indexes
CREATE INDEX idx_equipment_farm ON equipment(farm_id);
CREATE INDEX idx_equipment_type ON equipment(type);
CREATE INDEX idx_equipment_active ON equipment(is_active);

-- Grain contracts indexes
CREATE INDEX idx_grain_contracts_farm ON grain_contracts(farm_id);
CREATE INDEX idx_grain_contracts_crop ON grain_contracts(crop_id);
CREATE INDEX idx_grain_contracts_status ON grain_contracts(status);
CREATE INDEX idx_grain_contracts_date ON grain_contracts(contract_date);

-- Market prices indexes
CREATE INDEX idx_market_prices_crop_date ON market_prices(crop_id, price_date);
CREATE INDEX idx_market_prices_date ON market_prices(price_date);

-- Crop plans indexes
CREATE INDEX idx_crop_plans_farm ON crop_plans(farm_id);
CREATE INDEX idx_crop_plans_field ON crop_plans(field_id);
CREATE INDEX idx_crop_plans_year ON crop_plans(plan_year);

-- Session indexes
CREATE INDEX idx_user_sessions_user ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_token ON user_sessions(session_token);
CREATE INDEX idx_user_sessions_expires ON user_sessions(expires_at);

-- Notification indexes
CREATE INDEX idx_notifications_user ON notifications(user_id);
CREATE INDEX idx_notifications_read ON notifications(is_read);
CREATE INDEX idx_notifications_created ON notifications(created_at);

-- Audit log indexes
CREATE INDEX idx_audit_logs_user ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_table ON audit_logs(table_name);
CREATE INDEX idx_audit_logs_created ON audit_logs(created_at);
