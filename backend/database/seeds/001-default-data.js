'use strict';

const bcrypt = require('bcrypt');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Insert default crops
    await queryInterface.bulkInsert('crops', [
      {
        id: '550e8400-e29b-41d4-a716-446655440001',
        name: 'Corn',
        category: 'grain',
        typical_yield_per_acre: 180.0,
        typical_price_per_unit: 5.50,
        unit_of_measure: 'bushels',
        growing_season_days: 120,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440002',
        name: 'Soybeans',
        category: 'grain',
        typical_yield_per_acre: 50.0,
        typical_price_per_unit: 12.00,
        unit_of_measure: 'bushels',
        growing_season_days: 100,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440003',
        name: 'Wheat',
        category: 'grain',
        typical_yield_per_acre: 60.0,
        typical_price_per_unit: 7.25,
        unit_of_measure: 'bushels',
        growing_season_days: 90,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440004',
        name: 'Cotton',
        category: 'fiber',
        typical_yield_per_acre: 800.0,
        typical_price_per_unit: 0.75,
        unit_of_measure: 'pounds',
        growing_season_days: 180,
        created_at: new Date(),
        updated_at: new Date()
      }
    ]);

    // Insert default expense categories
    await queryInterface.bulkInsert('expense_categories', [
      {
        id: '550e8400-e29b-41d4-a716-446655440010',
        name: 'Seeds',
        description: 'Seed costs for planting',
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440011',
        name: 'Fertilizer',
        description: 'Fertilizer and nutrient costs',
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440012',
        name: 'Pesticides',
        description: 'Herbicides, insecticides, and fungicides',
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440013',
        name: 'Fuel',
        description: 'Fuel costs for equipment and vehicles',
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440014',
        name: 'Equipment Maintenance',
        description: 'Maintenance and repair costs for equipment',
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440015',
        name: 'Labor',
        description: 'Labor costs including wages and benefits',
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440016',
        name: 'Insurance',
        description: 'Crop and equipment insurance',
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440017',
        name: 'Utilities',
        description: 'Electricity, water, and other utilities',
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      }
    ]);

    // Insert default input categories
    await queryInterface.bulkInsert('input_categories', [
      {
        id: '550e8400-e29b-41d4-a716-446655440020',
        name: 'Nitrogen Fertilizer',
        description: 'Nitrogen-based fertilizers',
        unit_of_measure: 'pounds',
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440021',
        name: 'Phosphorus Fertilizer',
        description: 'Phosphorus-based fertilizers',
        unit_of_measure: 'pounds',
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440022',
        name: 'Potassium Fertilizer',
        description: 'Potassium-based fertilizers',
        unit_of_measure: 'pounds',
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440023',
        name: 'Herbicide',
        description: 'Weed control chemicals',
        unit_of_measure: 'gallons',
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440024',
        name: 'Insecticide',
        description: 'Insect control chemicals',
        unit_of_measure: 'gallons',
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440025',
        name: 'Fungicide',
        description: 'Disease control chemicals',
        unit_of_measure: 'gallons',
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      }
    ]);

    // Create default admin user
    const saltRounds = 12;
    const salt = await bcrypt.genSalt(saltRounds);
    const hashedPassword = await bcrypt.hash('admin123!', salt);

    await queryInterface.bulkInsert('users', [
      {
        id: '550e8400-e29b-41d4-a716-446655440100',
        email: '<EMAIL>',
        password_hash: hashedPassword,
        salt: salt,
        first_name: 'System',
        last_name: 'Administrator',
        phone: '******-0100',
        role: 'admin',
        is_active: true,
        email_verified: true,
        created_at: new Date(),
        updated_at: new Date()
      }
    ]);

    // Create demo farm
    await queryInterface.bulkInsert('farms', [
      {
        id: '550e8400-e29b-41d4-a716-446655440200',
        name: 'Demo Farm',
        owner_id: '550e8400-e29b-41d4-a716-446655440100',
        address: '123 Farm Road',
        city: 'Farmville',
        state: 'Iowa',
        zip_code: '50001',
        country: 'USA',
        total_acres: 1000.00,
        farm_type: 'Row Crop',
        established_year: 2010,
        tax_id: '12-3456789',
        created_at: new Date(),
        updated_at: new Date()
      }
    ]);

    // Create demo fields
    await queryInterface.bulkInsert('fields', [
      {
        id: '550e8400-e29b-41d4-a716-446655440300',
        farm_id: '550e8400-e29b-41d4-a716-446655440200',
        name: 'North Field',
        acres: 250.00,
        soil_type: 'Loam',
        drainage_class: 'Well Drained',
        slope_percentage: 2.5,
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440301',
        farm_id: '550e8400-e29b-41d4-a716-446655440200',
        name: 'South Field',
        acres: 300.00,
        soil_type: 'Clay Loam',
        drainage_class: 'Moderately Well Drained',
        slope_percentage: 1.8,
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440302',
        farm_id: '550e8400-e29b-41d4-a716-446655440200',
        name: 'East Field',
        acres: 200.00,
        soil_type: 'Sandy Loam',
        drainage_class: 'Well Drained',
        slope_percentage: 3.2,
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440303',
        farm_id: '550e8400-e29b-41d4-a716-446655440200',
        name: 'West Field',
        acres: 250.00,
        soil_type: 'Silt Loam',
        drainage_class: 'Well Drained',
        slope_percentage: 2.0,
        is_active: true,
        created_at: new Date(),
        updated_at: new Date()
      }
    ]);
  },

  async down(queryInterface, Sequelize) {
    // Remove seed data in reverse order
    await queryInterface.bulkDelete('fields', null, {});
    await queryInterface.bulkDelete('farms', null, {});
    await queryInterface.bulkDelete('users', null, {});
    await queryInterface.bulkDelete('input_categories', null, {});
    await queryInterface.bulkDelete('expense_categories', null, {});
    await queryInterface.bulkDelete('crops', null, {});
  }
};
