-- Database initialization script for Dock<PERSON>
-- This script runs when the PostgreSQL container starts for the first time

-- Create the database if it doesn't exist
SELECT 'CREATE DATABASE harvest_profit_pro'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'harvest_profit_pro')\gexec

-- Create test database
SELECT 'CREATE DATABASE harvest_profit_pro_test'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'harvest_profit_pro_test')\gexec

-- Connect to the main database
\c harvest_profit_pro;

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm"; -- For text search
CREATE EXTENSION IF NOT EXISTS "btree_gin"; -- For advanced indexing

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Grant necessary permissions
GRANT ALL PRIVILEGES ON DATABASE harvest_profit_pro TO harvest_user;
GRANT ALL PRIVILEGES ON DATABASE harvest_profit_pro_test TO harvest_user;
