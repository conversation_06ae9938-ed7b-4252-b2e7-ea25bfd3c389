#!/usr/bin/env node

/**
 * Phase 1 Validation Script
 * Validates that all Phase 1 components are properly implemented
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

console.log('🔍 Validating Phase 1 Implementation...\n');

// Track validation results
const results = {
  passed: 0,
  failed: 0,
  warnings: 0
};

/**
 * Validation helper functions
 */
const checkFileExists = (filePath, description) => {
  const fullPath = join(projectRoot, filePath);
  if (fs.existsSync(fullPath)) {
    console.log(`✅ ${description}: ${filePath}`);
    results.passed++;
    return true;
  } else {
    console.log(`❌ ${description}: ${filePath} - NOT FOUND`);
    results.failed++;
    return false;
  }
};

const checkDirectoryExists = (dirPath, description) => {
  const fullPath = join(projectRoot, dirPath);
  if (fs.existsSync(fullPath) && fs.statSync(fullPath).isDirectory()) {
    console.log(`✅ ${description}: ${dirPath}`);
    results.passed++;
    return true;
  } else {
    console.log(`❌ ${description}: ${dirPath} - NOT FOUND`);
    results.failed++;
    return false;
  }
};

const checkFileContent = (filePath, searchString, description) => {
  const fullPath = join(projectRoot, filePath);
  if (fs.existsSync(fullPath)) {
    const content = fs.readFileSync(fullPath, 'utf8');
    if (content.includes(searchString)) {
      console.log(`✅ ${description}`);
      results.passed++;
      return true;
    } else {
      console.log(`⚠️  ${description} - Content not found`);
      results.warnings++;
      return false;
    }
  } else {
    console.log(`❌ ${description} - File not found: ${filePath}`);
    results.failed++;
    return false;
  }
};

/**
 * Phase 1 Validation Checks
 */

console.log('📁 Checking Project Structure...');
checkDirectoryExists('src', 'Source directory');
checkDirectoryExists('src/config', 'Configuration directory');
checkDirectoryExists('src/controllers', 'Controllers directory');
checkDirectoryExists('src/middleware', 'Middleware directory');
checkDirectoryExists('src/models', 'Models directory');
checkDirectoryExists('src/routes', 'Routes directory');
checkDirectoryExists('src/services', 'Services directory');
checkDirectoryExists('src/utils', 'Utilities directory');
checkDirectoryExists('database', 'Database directory');
checkDirectoryExists('database/migrations', 'Migrations directory');
checkDirectoryExists('database/seeds', 'Seeds directory');
checkDirectoryExists('tests', 'Tests directory');

console.log('\n📋 Checking Configuration Files...');
checkFileExists('package.json', 'Package configuration');
checkFileExists('.env.example', 'Environment template');
checkFileExists('.sequelizerc', 'Sequelize configuration');
checkFileExists('Dockerfile', 'Docker configuration');

console.log('\n🔧 Checking Core Application Files...');
checkFileExists('src/app.js', 'Express application');
checkFileExists('src/server.js', 'Server entry point');
checkFileExists('src/config/index.js', 'Main configuration');
checkFileExists('src/config/database.js', 'Database configuration');

console.log('\n🗄️  Checking Database Components...');
checkFileExists('database/schema.sql', 'Database schema');
checkFileExists('database/migrations/001-initial-schema.js', 'Initial migration');
checkFileExists('database/seeds/001-default-data.js', 'Seed data');
checkFileExists('database/init/01-init.sql', 'Database initialization');

console.log('\n📊 Checking Models...');
checkFileExists('src/models/index.js', 'Models index');
checkFileExists('src/models/User.js', 'User model');
checkFileExists('src/models/Farm.js', 'Farm model');
checkFileExists('src/models/Field.js', 'Field model');
checkFileExists('src/models/Expense.js', 'Expense model');
checkFileExists('src/models/ExpenseCategory.js', 'ExpenseCategory model');
checkFileExists('src/models/UserSession.js', 'UserSession model');
checkFileExists('src/models/associations.js', 'Model associations');

console.log('\n🔐 Checking Authentication System...');
checkFileExists('src/utils/jwt.js', 'JWT utilities');
checkFileExists('src/utils/password.js', 'Password utilities');
checkFileExists('src/middleware/auth.js', 'Authentication middleware');
checkFileExists('src/services/AuthService.js', 'Authentication service');
checkFileExists('src/controllers/AuthController.js', 'Authentication controller');
checkFileExists('src/routes/auth.js', 'Authentication routes');

console.log('\n🛡️  Checking Security Components...');
checkFileExists('src/middleware/security.js', 'Security middleware');
checkFileExists('src/middleware/cors.js', 'CORS middleware');
checkFileExists('src/middleware/rateLimit.js', 'Rate limiting middleware');
checkFileExists('src/utils/sanitization.js', 'Input sanitization');

console.log('\n📝 Checking Error Handling & Logging...');
checkFileExists('src/utils/logger.js', 'Winston logger');
checkFileExists('src/utils/errors.js', 'Custom error classes');
checkFileExists('src/middleware/errorHandler.js', 'Error handling middleware');
checkFileExists('src/utils/response.js', 'Response utilities');

console.log('\n🧪 Checking Test Setup...');
checkFileExists('tests/setup.js', 'Test setup');
checkFileExists('tests/unit/auth.test.js', 'Authentication tests');
checkFileExists('jest.config.js', 'Jest configuration');

console.log('\n🔍 Checking Key Implementation Details...');
checkFileContent('src/models/User.js', 'bcrypt', 'User model has password hashing');
checkFileContent('src/utils/jwt.js', 'generateTokenPair', 'JWT utilities have token generation');
checkFileContent('src/middleware/auth.js', 'authenticate', 'Auth middleware has authentication function');
checkFileContent('src/middleware/security.js', 'sanitizeInputs', 'Security middleware has input sanitization');
checkFileContent('src/utils/logger.js', 'winston', 'Logger uses Winston');
checkFileContent('src/app.js', 'helmet', 'App uses Helmet for security');

console.log('\n📦 Checking Package Dependencies...');
const packageJsonPath = join(projectRoot, 'package.json');
if (fs.existsSync(packageJsonPath)) {
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  const requiredDeps = [
    'express', 'sequelize', 'pg', 'bcrypt', 'jsonwebtoken', 
    'helmet', 'cors', 'express-rate-limit', 'express-validator',
    'winston', 'winston-daily-rotate-file', 'dotenv'
  ];
  
  const missingDeps = requiredDeps.filter(dep => !packageJson.dependencies[dep]);
  
  if (missingDeps.length === 0) {
    console.log('✅ All required dependencies are present');
    results.passed++;
  } else {
    console.log(`❌ Missing dependencies: ${missingDeps.join(', ')}`);
    results.failed++;
  }
} else {
  console.log('❌ package.json not found');
  results.failed++;
}

/**
 * Validation Summary
 */
console.log('\n' + '='.repeat(50));
console.log('📊 PHASE 1 VALIDATION SUMMARY');
console.log('='.repeat(50));
console.log(`✅ Passed: ${results.passed}`);
console.log(`❌ Failed: ${results.failed}`);
console.log(`⚠️  Warnings: ${results.warnings}`);
console.log(`📈 Success Rate: ${Math.round((results.passed / (results.passed + results.failed)) * 100)}%`);

if (results.failed === 0) {
  console.log('\n🎉 Phase 1 validation PASSED! All core components are implemented.');
  console.log('\n📋 Phase 1 Deliverables Status:');
  console.log('✅ 1.1 Database Schema Design & Implementation');
  console.log('✅ 1.2 Sequelize ORM Setup & Models');
  console.log('✅ 1.3 Authentication System Implementation');
  console.log('✅ 1.4 Core API Infrastructure');
  console.log('✅ 1.5 Security Implementation');
  console.log('✅ 1.6 Error Handling & Logging');
  
  console.log('\n🚀 Ready for Phase 2: Core APIs Implementation');
} else {
  console.log('\n⚠️  Phase 1 validation has issues that need to be addressed.');
  console.log('Please fix the failed checks before proceeding to Phase 2.');
}

console.log('\n' + '='.repeat(50));

process.exit(results.failed === 0 ? 0 : 1);
