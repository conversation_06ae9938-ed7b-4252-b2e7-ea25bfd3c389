# Environment Configuration Template
# Copy this file to .env and update the values

# Application
NODE_ENV=development
PORT=5000
API_VERSION=v1

# Database Configuration
DATABASE_URL=postgresql://harvest_user:harvest_password_dev@localhost:5432/harvest_profit_pro
DB_HOST=localhost
DB_PORT=5432
DB_NAME=harvest_profit_pro
DB_USER=harvest_user
DB_PASSWORD=harvest_password_dev
DB_DIALECT=postgres
DB_LOGGING=true

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# Password Hashing
BCRYPT_ROUNDS=12

# CORS Configuration
CORS_ORIGIN=http://localhost:3000
CORS_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_AUTH_MAX_REQUESTS=5

# Redis Configuration (for sessions and caching)
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_MAX_SIZE=20m
LOG_FILE_MAX_FILES=14d

# Security
HELMET_CSP_ENABLED=true
CSRF_SECRET=your-csrf-secret-key

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_PATH=uploads/

# External APIs (for future use)
WEATHER_API_KEY=
COMMODITY_API_KEY=

# Email Configuration (for password reset)
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASSWORD=
EMAIL_FROM=<EMAIL>

# Monitoring and Analytics
SENTRY_DSN=
ANALYTICS_ENABLED=false
