// The exported code uses Tailwind CSS. Install Tailwind CSS in your dev environment to ensure all styles work.

import React, { useState } from 'react';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { ScrollArea } from "@/components/ui/scroll-area";
import * as echarts from 'echarts';

const App: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [activeTab, setActiveTab] = useState('dashboard');

  React.useEffect(() => {
    // Revenue Chart
    const revenueChart = echarts.init(document.getElementById('revenue-chart'));
    const revenueOption = {
      animation: false,
      title: {
        text: 'Monthly Revenue',
        textStyle: { fontSize: 14, color: '#333333' }
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun']
      },
      yAxis: {
        type: 'value'
      },
      series: [{
        data: [12000, 15000, 18000, 22000, 25000, 28000],
        type: 'line',
        smooth: true,
        lineStyle: { color: '#4CAF50' },
        itemStyle: { color: '#4CAF50' }
      }]
    };
    revenueChart.setOption(revenueOption);

    // Expense Chart
    const expenseChart = echarts.init(document.getElementById('expense-chart'));
    const expenseOption = {
      animation: false,
      title: {
        text: 'Expense Breakdown',
        textStyle: { fontSize: 14, color: '#333333' }
      },
      tooltip: {
        trigger: 'item'
      },
      series: [{
        type: 'pie',
        radius: '70%',
        data: [
          { value: 8000, name: 'Fertilizer', itemStyle: { color: '#4CAF50' } },
          { value: 6000, name: 'Fuel', itemStyle: { color: '#8BC34A' } },
          { value: 4000, name: 'Equipment', itemStyle: { color: '#FFC107' } },
          { value: 3000, name: 'Labor', itemStyle: { color: '#FF9800' } },
          { value: 2000, name: 'Other', itemStyle: { color: '#9E9E9E' } }
        ]
      }]
    };
    expenseChart.setOption(expenseOption);

    // Profit Chart
    const profitChart = echarts.init(document.getElementById('profit-chart'));
    const profitOption = {
      animation: false,
      title: {
        text: 'Profit per Acre by Field',
        textStyle: { fontSize: 14, color: '#333333' }
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: ['Field A', 'Field B', 'Field C', 'Field D', 'Field E']
      },
      yAxis: {
        type: 'value'
      },
      series: [{
        data: [450, 380, 520, 390, 470],
        type: 'bar',
        itemStyle: { color: '#4CAF50' }
      }]
    };
    profitChart.setOption(profitOption);

    return () => {
      revenueChart.dispose();
      expenseChart.dispose();
      profitChart.dispose();
    };
  }, []);

  const navigationItems = [
    { id: 'dashboard', label: 'Dashboard', icon: 'fas fa-tachometer-alt' },
    { id: 'expenses', label: 'Expenses', icon: 'fas fa-receipt' },
    { id: 'profitability', label: 'Profitability', icon: 'fas fa-chart-line' },
    { id: 'inputs', label: 'Inputs', icon: 'fas fa-seedling' },
    { id: 'equipment', label: 'Equipment', icon: 'fas fa-tractor' },
    { id: 'financials', label: 'Financials', icon: 'fas fa-dollar-sign' },
    { id: 'grain-marketing', label: 'Grain Marketing', icon: 'fas fa-wheat' },
    { id: 'crop-planning', label: 'Crop Planning', icon: 'fas fa-calendar-alt' },
    { id: 'capital', label: 'Capital', icon: 'fas fa-coins' },
    { id: 'reports', label: 'Reports', icon: 'fas fa-file-alt' },
    { id: 'settings', label: 'Settings', icon: 'fas fa-cog' }
  ];

  const renderDashboard = () => (
    <div className="space-y-6">
      {/* Hero Section */}
      <div 
        className="relative h-64 rounded-lg overflow-hidden"
        style={{
          backgroundImage: `url('https://readdy.ai/api/search-image?query=Modern%20agricultural%20landscape%20with%20green%20fields%20and%20farming%20equipment%20under%20clear%20blue%20sky%2C%20professional%20photography%20style%20with%20soft%20natural%20lighting%20and%20vibrant%20colors&width=1200&height=256&seq=hero-bg-001&orientation=landscape')`,
          backgroundSize: 'cover',
          backgroundPosition: 'center'
        }}
      >
        <div className="absolute inset-0 bg-gradient-to-r from-green-900/80 to-transparent"></div>
        <div className="relative z-10 h-full flex items-center px-8">
          <div className="text-white">
            <h1 className="text-4xl font-bold mb-2">Welcome to Harvest Profit Pro</h1>
            <p className="text-xl opacity-90">Track your farm's financial performance with precision</p>
            <Button className="mt-4 !rounded-button whitespace-nowrap cursor-pointer" style={{ backgroundColor: '#FFC107', color: '#333333' }}>
              Get Started
            </Button>
          </div>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="bg-white shadow-lg hover:shadow-xl transition-shadow duration-300">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Total Revenue</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">$128,450</div>
            <p className="text-xs text-gray-500 mt-1">
              <span className="text-green-500">+12.5%</span> from last month
            </p>
          </CardContent>
        </Card>

        <Card className="bg-white shadow-lg hover:shadow-xl transition-shadow duration-300">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Total Expenses</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">$85,320</div>
            <p className="text-xs text-gray-500 mt-1">
              <span className="text-red-500">+8.2%</span> from last month
            </p>
          </CardContent>
        </Card>

        <Card className="bg-white shadow-lg hover:shadow-xl transition-shadow duration-300">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Net Profit</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">$43,130</div>
            <p className="text-xs text-gray-500 mt-1">
              <span className="text-green-500">+18.7%</span> from last month
            </p>
          </CardContent>
        </Card>

        <Card className="bg-white shadow-lg hover:shadow-xl transition-shadow duration-300">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Profit per Acre</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">$432</div>
            <p className="text-xs text-gray-500 mt-1">
              <span className="text-green-500">+15.3%</span> from last month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="bg-white shadow-lg">
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-gray-800">Revenue Trend</CardTitle>
          </CardHeader>
          <CardContent>
            <div id="revenue-chart" style={{ width: '100%', height: '300px' }}></div>
          </CardContent>
        </Card>

        <Card className="bg-white shadow-lg">
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-gray-800">Expense Breakdown</CardTitle>
          </CardHeader>
          <CardContent>
            <div id="expense-chart" style={{ width: '100%', height: '300px' }}></div>
          </CardContent>
        </Card>
      </div>

      {/* Profit Analysis */}
      <Card className="bg-white shadow-lg">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-gray-800">Field Profitability Analysis</CardTitle>
        </CardHeader>
        <CardContent>
          <div id="profit-chart" style={{ width: '100%', height: '300px' }}></div>
        </CardContent>
      </Card>

      {/* Recent Activities */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="bg-white shadow-lg">
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-gray-800">Recent Expenses</CardTitle>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-64">
              <div className="space-y-4">
                {[
                  { date: '2024-01-15', description: 'Fertilizer Purchase - Field A', amount: '$2,450', category: 'Inputs' },
                  { date: '2024-01-14', description: 'Fuel for Tractor Maintenance', amount: '$380', category: 'Equipment' },
                  { date: '2024-01-13', description: 'Seed Purchase - Corn', amount: '$1,200', category: 'Inputs' },
                  { date: '2024-01-12', description: 'Labor - Harvesting', amount: '$850', category: 'Labor' },
                  { date: '2024-01-11', description: 'Equipment Repair', amount: '$675', category: 'Equipment' }
                ].map((expense, index) => (
                  <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium text-gray-800">{expense.description}</p>
                      <p className="text-sm text-gray-500">{expense.date}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-red-600">{expense.amount}</p>
                      <Badge variant="secondary" className="text-xs">{expense.category}</Badge>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>

        <Card className="bg-white shadow-lg">
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-gray-800">Field Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { field: 'Field A - Corn', acres: 125, yield: '185 bu/acre', profit: '$450/acre', status: 'Excellent' },
                { field: 'Field B - Soybeans', acres: 98, yield: '52 bu/acre', profit: '$380/acre', status: 'Good' },
                { field: 'Field C - Wheat', acres: 87, yield: '68 bu/acre', profit: '$520/acre', status: 'Excellent' },
                { field: 'Field D - Corn', acres: 156, yield: '178 bu/acre', profit: '$390/acre', status: 'Good' }
              ].map((field, index) => (
                <div key={index} className="p-4 bg-gray-50 rounded-lg">
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="font-medium text-gray-800">{field.field}</h4>
                    <Badge 
                      variant={field.status === 'Excellent' ? 'default' : 'secondary'}
                      className={field.status === 'Excellent' ? 'bg-green-500' : 'bg-yellow-500'}
                    >
                      {field.status}
                    </Badge>
                  </div>
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <p className="text-gray-500">Acres</p>
                      <p className="font-semibold">{field.acres}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Yield</p>
                      <p className="font-semibold">{field.yield}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Profit</p>
                      <p className="font-semibold text-green-600">{field.profit}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const renderExpenses = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-800">Expense Management</h2>
        <Button className="!rounded-button whitespace-nowrap cursor-pointer" style={{ backgroundColor: '#4CAF50', color: 'white' }}>
          <i className="fas fa-plus mr-2"></i>
          Add Expense
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="bg-white shadow-lg">
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-gray-800">Quick Add Expense</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="expense-type">Expense Type</Label>
              <div className="relative">
                <Button variant="outline" className="w-full justify-between !rounded-button whitespace-nowrap cursor-pointer">
                  Select Category
                  <i className="fas fa-chevron-down"></i>
                </Button>
              </div>
            </div>
            <div>
              <Label htmlFor="amount">Amount</Label>
              <Input 
                id="amount" 
                type="number" 
                placeholder="Enter amount" 
                className="border border-gray-300 rounded-lg text-sm"
              />
            </div>
            <div>
              <Label htmlFor="description">Description</Label>
              <Input 
                id="description" 
                placeholder="Enter description" 
                className="border border-gray-300 rounded-lg text-sm"
              />
            </div>
            <div>
              <Label htmlFor="field">Field</Label>
              <div className="relative">
                <Button variant="outline" className="w-full justify-between !rounded-button whitespace-nowrap cursor-pointer">
                  Select Field
                  <i className="fas fa-chevron-down"></i>
                </Button>
              </div>
            </div>
            <Button className="w-full !rounded-button whitespace-nowrap cursor-pointer" style={{ backgroundColor: '#4CAF50', color: 'white' }}>
              Add Expense
            </Button>
          </CardContent>
        </Card>

        <Card className="bg-white shadow-lg md:col-span-2">
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-gray-800">Recent Expenses</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {[
                { date: '2024-01-15', category: 'Fertilizer', description: 'NPK Fertilizer - Field A', amount: '$2,450', field: 'Field A' },
                { date: '2024-01-14', category: 'Fuel', description: 'Diesel for Tractor', amount: '$380', field: 'All Fields' },
                { date: '2024-01-13', category: 'Seeds', description: 'Corn Seed Purchase', amount: '$1,200', field: 'Field C' },
                { date: '2024-01-12', category: 'Labor', description: 'Harvesting Labor', amount: '$850', field: 'Field B' },
                { date: '2024-01-11', category: 'Equipment', description: 'Tractor Maintenance', amount: '$675', field: 'All Fields' }
              ].map((expense, index) => (
                <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                      <i className="fas fa-receipt text-red-600"></i>
                    </div>
                    <div>
                      <p className="font-medium text-gray-800">{expense.description}</p>
                      <p className="text-sm text-gray-500">{expense.date} • {expense.field}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-red-600">{expense.amount}</p>
                    <Badge variant="secondary" className="text-xs">{expense.category}</Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <Card className="bg-white shadow-lg">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-gray-800">Expense Categories</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[
              { category: 'Fertilizer', amount: '$12,450', percentage: 35, color: '#4CAF50' },
              { category: 'Fuel', amount: '$8,320', percentage: 25, color: '#8BC34A' },
              { category: 'Equipment', amount: '$6,780', percentage: 20, color: '#FFC107' },
              { category: 'Labor', amount: '$4,890', percentage: 15, color: '#FF9800' },
              { category: 'Seeds', amount: '$3,210', percentage: 10, color: '#9E9E9E' },
              { category: 'Insurance', amount: '$2,150', percentage: 8, color: '#607D8B' },
              { category: 'Utilities', amount: '$1,890', percentage: 6, color: '#795548' },
              { category: 'Other', amount: '$1,200', percentage: 4, color: '#9C27B0' }
            ].map((item, index) => (
              <div key={index} className="p-4 bg-gray-50 rounded-lg">
                <div className="flex justify-between items-center mb-2">
                  <h4 className="font-medium text-gray-800">{item.category}</h4>
                  <span className="text-sm font-semibold text-gray-600">{item.amount}</span>
                </div>
                <Progress value={item.percentage} className="h-2" />
                <p className="text-xs text-gray-500 mt-1">{item.percentage}% of total</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return renderDashboard();
      case 'expenses':
        return renderExpenses();
      case 'profitability':
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-800">Profitability Analysis</h2>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-white shadow-lg">
                <CardHeader>
                  <CardTitle className="text-lg font-semibold text-gray-800">Profit Margins by Crop</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {[
                      { crop: 'Corn', margin: '28%', profit: '$450/acre', color: '#4CAF50' },
                      { crop: 'Soybeans', margin: '22%', profit: '$380/acre', color: '#8BC34A' },
                      { crop: 'Wheat', margin: '35%', profit: '$520/acre', color: '#FFC107' }
                    ].map((item, index) => (
                      <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div>
                          <h4 className="font-medium text-gray-800">{item.crop}</h4>
                          <p className="text-sm text-gray-500">Profit: {item.profit}</p>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold text-green-600">{item.margin}</p>
                          <Progress value={parseInt(item.margin)} className="w-20 h-2" />
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
              <Card className="bg-white shadow-lg">
                <CardHeader>
                  <CardTitle className="text-lg font-semibold text-gray-800">Cost Analysis</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="text-center">
                      <p className="text-3xl font-bold text-green-600">$285/acre</p>
                      <p className="text-sm text-gray-500">Average Cost per Acre</p>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Variable Costs</span>
                        <span className="text-sm font-semibold">$185/acre</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Fixed Costs</span>
                        <span className="text-sm font-semibold">$100/acre</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        );
      default:
        return (
          <div className="text-center py-12">
            <i className="fas fa-construction text-6xl text-gray-400 mb-4"></i>
            <h3 className="text-xl font-semibold text-gray-600 mb-2">Coming Soon</h3>
            <p className="text-gray-500">This feature is under development</p>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Top Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-50 bg-green-600 text-white shadow-lg">
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="text-white hover:bg-green-700 !rounded-button whitespace-nowrap cursor-pointer"
            >
              <i className="fas fa-bars"></i>
            </Button>
            <h1 className="text-xl font-bold">Harvest Profit Pro</h1>
          </div>
          <div className="flex items-center space-x-6">
            <nav className="hidden md:flex space-x-6">
              <Button variant="ghost" className="text-white hover:bg-green-700 !rounded-button whitespace-nowrap cursor-pointer">
                Dashboard
              </Button>
              <Button variant="ghost" className="text-white hover:bg-green-700 !rounded-button whitespace-nowrap cursor-pointer">
                Reports
              </Button>
              <Button variant="ghost" className="text-white hover:bg-green-700 !rounded-button whitespace-nowrap cursor-pointer">
                Analytics
              </Button>
            </nav>
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="sm" className="text-white hover:bg-green-700 !rounded-button whitespace-nowrap cursor-pointer">
                <i className="fas fa-bell"></i>
              </Button>
              <Button variant="ghost" size="sm" className="text-white hover:bg-green-700 !rounded-button whitespace-nowrap cursor-pointer">
                <i className="fas fa-user-circle"></i>
              </Button>
            </div>
          </div>
        </div>
      </nav>

      <div className="flex pt-16">
        {/* Sidebar */}
        <div className={`fixed left-0 top-16 h-full bg-white shadow-lg transition-transform duration-300 z-40 ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}`} style={{ width: '250px' }}>
          <ScrollArea className="h-full">
            <div className="p-4">
              <nav className="space-y-2">
                {navigationItems.map((item) => (
                  <Button
                    key={item.id}
                    variant={activeTab === item.id ? 'default' : 'ghost'}
                    className={`w-full justify-start !rounded-button whitespace-nowrap cursor-pointer ${
                      activeTab === item.id 
                        ? 'bg-green-600 text-white' 
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                    onClick={() => setActiveTab(item.id)}
                  >
                    <i className={`${item.icon} mr-3`}></i>
                    {item.label}
                  </Button>
                ))}
              </nav>
            </div>
          </ScrollArea>
        </div>

        {/* Main Content */}
        <div className={`flex-1 transition-all duration-300 ${sidebarOpen ? 'ml-64' : 'ml-0'}`}>
          <main className="p-6">
            {renderContent()}
          </main>
        </div>
      </div>
    </div>
  );
};

export default App;
