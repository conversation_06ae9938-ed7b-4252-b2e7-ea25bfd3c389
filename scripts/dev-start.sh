#!/bin/bash

# Harvest Profit Pro Development Start Script
# This script starts the development environment

set -e

echo "🌾 Starting Harvest Profit Pro Development Environment..."

# Check if .env.local exists
if [ ! -f .env.local ]; then
    echo "❌ .env.local not found. Please run ./scripts/dev-setup.sh first."
    exit 1
fi

# Start Docker services
echo "🐳 Starting Docker services..."
docker-compose up -d postgres redis

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 5

# Check if PostgreSQL is ready
until docker-compose exec postgres pg_isready -U harvest_user -d harvest_profit_pro; do
    echo "Waiting for PostgreSQL..."
    sleep 2
done

echo "✅ Services are ready"

# Start frontend development server
echo "🚀 Starting frontend development server..."
npm run dev

echo "🎉 Development environment is running!"
echo "Frontend: http://localhost:3000"
echo "Backend API: http://localhost:5000 (when backend is running)"
echo "PostgreSQL: localhost:5432"
echo "Redis: localhost:6379"
