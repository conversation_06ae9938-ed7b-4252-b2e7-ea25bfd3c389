#!/bin/bash

# Harvest Profit Pro Development Setup Script
# This script sets up the development environment

set -e

echo "🌾 Setting up Harvest Profit Pro Development Environment..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18 or higher is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Prerequisites check passed"

# Create environment file if it doesn't exist
if [ ! -f .env.local ]; then
    echo "📝 Creating .env.local from .env.example..."
    cp .env.example .env.local
    echo "⚠️  Please update .env.local with your actual API keys and configuration"
fi

# Install frontend dependencies
echo "📦 Installing frontend dependencies..."
npm install

# Create backend directory structure if it doesn't exist
if [ ! -d "backend" ]; then
    echo "📁 Creating backend directory structure..."
    mkdir -p backend/{src,database/init,tests}
fi

# Start Docker services
echo "🐳 Starting Docker services..."
docker-compose up -d postgres redis

# Wait for PostgreSQL to be ready
echo "⏳ Waiting for PostgreSQL to be ready..."
until docker-compose exec postgres pg_isready -U harvest_user -d harvest_profit_pro; do
    sleep 2
done

echo "✅ PostgreSQL is ready"

# Install backend dependencies (if backend exists)
if [ -f "backend/package.json" ]; then
    echo "📦 Installing backend dependencies..."
    cd backend && npm install && cd ..
fi

echo ""
echo "🎉 Development environment setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Update .env.local with your API keys"
echo "2. Run 'npm run dev' to start the frontend development server"
echo "3. Run 'npm run backend:dev' to start the backend server (when backend is ready)"
echo "4. Visit http://localhost:3000 to view the application"
echo ""
echo "🔧 Useful commands:"
echo "- npm run dev              # Start frontend development server"
echo "- npm run backend:dev      # Start backend development server"
echo "- docker-compose up -d     # Start all services in background"
echo "- docker-compose down      # Stop all services"
echo "- docker-compose logs -f   # View logs"
echo ""
