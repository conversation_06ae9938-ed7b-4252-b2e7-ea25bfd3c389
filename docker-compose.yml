version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: harvest-profit-db
    restart: unless-stopped
    environment:
      POSTGRES_DB: harvest_profit_pro
      POSTGRES_USER: harvest_user
      POSTGRES_PASSWORD: harvest_password_dev
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/database/init:/docker-entrypoint-initdb.d
    networks:
      - harvest-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U harvest_user -d harvest_profit_pro"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API Server
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    container_name: harvest-profit-backend
    restart: unless-stopped
    environment:
      NODE_ENV: development
      PORT: 5000
      DATABASE_URL: ************************************************************/harvest_profit_pro
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
      JWT_EXPIRES_IN: 7d
      BCRYPT_ROUNDS: 12
      CORS_ORIGIN: http://localhost:3000
      RATE_LIMIT_WINDOW_MS: 900000
      RATE_LIMIT_MAX_REQUESTS: 100
    ports:
      - "5000:5000"
    volumes:
      - ./backend:/app
      - /app/node_modules
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - harvest-network
    command: npm run dev

  # Frontend Development Server
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
      target: development
    container_name: harvest-profit-frontend
    restart: unless-stopped
    environment:
      NODE_ENV: development
      VITE_API_URL: http://localhost:5000/api
      VITE_WS_URL: ws://localhost:5000
      VITE_APP_NAME: Harvest Profit Pro
      VITE_APP_VERSION: 1.0.0
    ports:
      - "3000:3000"
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - harvest-network
    command: npm run dev

  # Redis for session storage and caching (optional)
  redis:
    image: redis:7-alpine
    container_name: harvest-profit-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - harvest-network
    command: redis-server --appendonly yes

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  harvest-network:
    driver: bridge
