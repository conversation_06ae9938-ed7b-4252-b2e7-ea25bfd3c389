# Phase 2: Core Feature APIs (Dashboard, Expenses, Profitability)

## Phase Overview
**Duration:** 3 weeks  
**Start Date:** 2024-01-30  
**Target End Date:** 2024-02-20  
**Dependencies:** Phase 1 (100% complete)  
**Risk Level:** Medium (Complex business logic)  

## Phase Objectives
1. Implement Dashboard API with real-time metrics aggregation
2. Create comprehensive Expense Tracking API with categorization
3. Build Profitability Analysis API with field-level calculations
4. Establish data aggregation services for cross-feature insights
5. Implement real-time data synchronization with frontend
6. Ensure seamless integration with existing Dashboard component

## Component Breakdown

### 2.1 Dashboard API Endpoints
**Priority:** Critical  
**Estimated Time:** 4 days  
**Dependencies:** Phase 1 (All models available)  

#### Files to Create/Modify:
- `backend/src/controllers/DashboardController.js` - Dashboard business logic
- `backend/src/services/DashboardService.js` - Data aggregation service
- `backend/src/routes/dashboard.js` - Dashboard API routes
- `backend/src/utils/metrics.js` - Metrics calculation utilities
- `backend/src/middleware/caching.js` - Response caching middleware

#### Acceptance Criteria:
- [ ] **2.1.1** GET /api/dashboard/metrics - Financial overview metrics
- [ ] **2.1.2** GET /api/dashboard/recent-activities - Latest farm activities
- [ ] **2.1.3** GET /api/dashboard/field-performance - Field profitability data
- [ ] **2.1.4** GET /api/dashboard/charts - Chart data for visualizations
- [ ] **2.1.5** Real-time metrics calculation with caching
- [ ] **2.1.6** Data aggregation across multiple time periods
- [ ] **2.1.7** Integration with existing DashboardContext

#### API Endpoints Specification:
```javascript
// GET /api/dashboard/metrics
Response: {
  totalRevenue: number,
  totalExpenses: number,
  netProfit: number,
  profitPerAcre: number,
  revenueChange: number,
  expensesChange: number,
  profitChange: number,
  profitPerAcreChange: number,
  lastUpdated: string
}

// GET /api/dashboard/recent-activities
Response: {
  expenses: Expense[],
  revenues: Revenue[],
  activities: Activity[],
  pagination: PaginationInfo
}

// GET /api/dashboard/field-performance
Response: {
  fields: [{
    id: string,
    name: string,
    acres: number,
    revenue: number,
    expenses: number,
    profit: number,
    profitPerAcre: number,
    status: string
  }]
}
```

#### Integration Points:
- [ ] **Frontend Context**: Updates DashboardContext state
- [ ] **WebSocket**: Real-time metric updates
- [ ] **Caching**: Redis caching for expensive calculations
- [ ] **Database**: Aggregates data from multiple tables

#### Testing Requirements:
- [ ] Unit tests for all controller methods
- [ ] Integration tests for API endpoints
- [ ] Performance tests for data aggregation
- [ ] Cache invalidation tests

### 2.2 Expense Tracking API
**Priority:** Critical  
**Estimated Time:** 5 days  
**Dependencies:** 2.1 (Dashboard metrics integration)  

#### Files to Create/Modify:
- `backend/src/controllers/ExpenseController.js` - Expense CRUD operations
- `backend/src/services/ExpenseService.js` - Expense business logic
- `backend/src/routes/expenses.js` - Expense API routes
- `backend/src/utils/expenseCalculations.js` - Expense calculation utilities
- `backend/src/middleware/fileUpload.js` - Receipt upload middleware

#### Acceptance Criteria:
- [ ] **2.2.1** GET /api/expenses - List expenses with filtering/pagination
- [ ] **2.2.2** POST /api/expenses - Create new expense
- [ ] **2.2.3** GET /api/expenses/:id - Get expense details
- [ ] **2.2.4** PUT /api/expenses/:id - Update expense
- [ ] **2.2.5** DELETE /api/expenses/:id - Delete expense
- [ ] **2.2.6** GET /api/expenses/categories - Get expense categories
- [ ] **2.2.7** POST /api/expenses/bulk - Bulk expense import
- [ ] **2.2.8** GET /api/expenses/summary - Expense analytics
- [ ] **2.2.9** File upload for receipts (images/PDFs)
- [ ] **2.2.10** Advanced filtering (date, category, amount, field)

#### API Endpoints Specification:
```javascript
// POST /api/expenses
Request: {
  date: string,
  categoryId: string,
  subcategoryId?: string,
  amount: number,
  description: string,
  notes?: string,
  fieldId?: string,
  tags?: string[],
  receiptFile?: File
}

// GET /api/expenses?page=1&limit=20&category=inputs&field=field-1
Response: {
  expenses: Expense[],
  pagination: {
    page: number,
    limit: number,
    total: number,
    totalPages: number
  },
  summary: {
    totalAmount: number,
    averageAmount: number,
    categoryBreakdown: CategorySummary[]
  }
}
```

#### Business Logic Requirements:
- [ ] **Categorization**: Automatic expense categorization suggestions
- [ ] **Field Association**: Link expenses to specific fields
- [ ] **Recurring Expenses**: Support for recurring expense patterns
- [ ] **Budget Tracking**: Compare expenses against budgets
- [ ] **Tax Categories**: Support for tax-deductible expense tracking

#### Testing Requirements:
- [ ] CRUD operation tests
- [ ] File upload tests
- [ ] Filtering and pagination tests
- [ ] Business logic validation tests
- [ ] Integration with dashboard metrics tests

### 2.3 Profitability Analysis API
**Priority:** High  
**Estimated Time:** 6 days  
**Dependencies:** 2.2 (Expense data available)  

#### Files to Create/Modify:
- `backend/src/controllers/ProfitabilityController.js` - Profitability calculations
- `backend/src/services/ProfitabilityService.js` - Complex profitability logic
- `backend/src/routes/profitability.js` - Profitability API routes
- `backend/src/utils/profitCalculations.js` - Profit calculation algorithms
- `backend/src/services/RecommendationService.js` - Improvement recommendations

#### Acceptance Criteria:
- [ ] **2.3.1** GET /api/profitability/fields - Field-level profitability
- [ ] **2.3.2** GET /api/profitability/crops - Crop-level profitability
- [ ] **2.3.3** GET /api/profitability/analysis - Comprehensive analysis
- [ ] **2.3.4** GET /api/profitability/trends - Historical trends
- [ ] **2.3.5** GET /api/profitability/recommendations - Improvement suggestions
- [ ] **2.3.6** POST /api/profitability/scenarios - What-if scenario analysis
- [ ] **2.3.7** Real-time profitability calculations
- [ ] **2.3.8** Benchmark comparisons with industry standards

#### Profitability Calculations:
```javascript
// Field Profitability Metrics
- Gross Revenue per Acre
- Total Expenses per Acre
- Net Profit per Acre
- Return on Investment (ROI)
- Break-even Analysis
- Cost per Unit of Production
- Profit Margin Percentage

// Crop Profitability Metrics
- Revenue per Crop Type
- Input Costs per Crop
- Labor Costs per Crop
- Equipment Costs per Crop
- Net Profit per Crop
- Yield Efficiency Metrics
```

#### API Endpoints Specification:
```javascript
// GET /api/profitability/fields
Response: {
  fields: [{
    fieldId: string,
    fieldName: string,
    acres: number,
    grossRevenue: number,
    totalExpenses: number,
    netProfit: number,
    profitPerAcre: number,
    roi: number,
    profitMargin: number,
    breakEvenPoint: number,
    recommendations: string[]
  }],
  summary: {
    totalProfit: number,
    averageProfitPerAcre: number,
    mostProfitableField: string,
    leastProfitableField: string
  }
}

// POST /api/profitability/scenarios
Request: {
  fieldId: string,
  scenarios: [{
    name: string,
    changes: {
      inputCosts?: number,
      yieldIncrease?: number,
      priceChange?: number
    }
  }]
}
```

#### Testing Requirements:
- [ ] Calculation accuracy tests
- [ ] Scenario analysis tests
- [ ] Performance tests for large datasets
- [ ] Integration tests with expense/revenue data

### 2.4 Data Aggregation Services
**Priority:** High  
**Estimated Time:** 3 days  
**Dependencies:** 2.1, 2.2, 2.3 (All core APIs)  

#### Files to Create/Modify:
- `backend/src/services/AggregationService.js` - Cross-feature data aggregation
- `backend/src/utils/dataTransformers.js` - Data transformation utilities
- `backend/src/jobs/dataAggregation.js` - Scheduled aggregation jobs
- `backend/src/cache/aggregationCache.js` - Caching for aggregated data

#### Acceptance Criteria:
- [ ] **2.4.1** Cross-feature data aggregation (expenses + revenue + fields)
- [ ] **2.4.2** Scheduled data aggregation jobs (daily/weekly/monthly)
- [ ] **2.4.3** Real-time aggregation triggers on data changes
- [ ] **2.4.4** Caching strategy for expensive aggregations
- [ ] **2.4.5** Data consistency validation across features
- [ ] **2.4.6** Historical data aggregation and archival

#### Aggregation Services:
```javascript
// Financial Aggregations
- Monthly/Quarterly/Annual summaries
- Category-wise expense rollups
- Field-wise profit calculations
- Trend analysis data
- Comparative period analysis

// Performance Aggregations
- Yield per acre calculations
- Cost efficiency metrics
- Resource utilization rates
- Equipment performance metrics
```

#### Testing Requirements:
- [ ] Aggregation accuracy tests
- [ ] Performance tests for large datasets
- [ ] Cache invalidation tests
- [ ] Scheduled job tests

### 2.5 Real-time Data Sync
**Priority:** Medium  
**Estimated Time:** 2 days  
**Dependencies:** 2.4 (Aggregation services)  

#### Files to Create/Modify:
- `backend/src/websocket/dashboardSocket.js` - WebSocket handlers
- `backend/src/services/RealtimeService.js` - Real-time update logic
- `backend/src/events/dataEvents.js` - Data change event emitters
- `backend/src/middleware/websocketAuth.js` - WebSocket authentication

#### Acceptance Criteria:
- [ ] **2.5.1** WebSocket connection for real-time updates
- [ ] **2.5.2** Real-time dashboard metric updates
- [ ] **2.5.3** Live expense tracking updates
- [ ] **2.5.4** Real-time profitability recalculations
- [ ] **2.5.5** Event-driven data synchronization
- [ ] **2.5.6** Connection management and reconnection logic

#### WebSocket Events:
```javascript
// Client Events
- 'dashboard:subscribe' - Subscribe to dashboard updates
- 'expenses:subscribe' - Subscribe to expense updates
- 'profitability:subscribe' - Subscribe to profitability updates

// Server Events
- 'dashboard:metrics:updated' - Dashboard metrics changed
- 'expenses:created' - New expense added
- 'expenses:updated' - Expense modified
- 'profitability:recalculated' - Profitability data updated
```

#### Testing Requirements:
- [ ] WebSocket connection tests
- [ ] Real-time update tests
- [ ] Authentication tests for WebSocket
- [ ] Event emission tests

## Phase Integration Requirements

### Frontend Integration
- [ ] **DashboardContext**: Update to use new API endpoints
- [ ] **ExpenseContext**: Integrate with expense API
- [ ] **ProfitabilityContext**: Connect to profitability API
- [ ] **WebSocket Integration**: Real-time updates in frontend
- [ ] **Error Handling**: Consistent error handling across APIs

### Database Integration
- [ ] **Data Consistency**: Ensure data integrity across features
- [ ] **Performance**: Optimize queries for large datasets
- [ ] **Indexing**: Create indexes for frequently queried data
- [ ] **Transactions**: Use database transactions for complex operations

### Security Integration
- [ ] **Authentication**: All endpoints require valid JWT
- [ ] **Authorization**: Role-based access to sensitive data
- [ ] **Input Validation**: Validate all API inputs
- [ ] **Rate Limiting**: Prevent API abuse

## Phase Validation Criteria

### Quality Gates
- [ ] **API Documentation**: OpenAPI specs for all endpoints
- [ ] **Test Coverage**: 85%+ coverage for all components
- [ ] **Performance**: API response times <300ms
- [ ] **Security**: Security audit passes
- [ ] **Integration**: Works seamlessly with existing frontend

### Business Logic Validation
- [ ] **Calculation Accuracy**: Financial calculations verified
- [ ] **Data Integrity**: Cross-feature data consistency
- [ ] **Real-time Updates**: Live data synchronization working
- [ ] **Error Handling**: Graceful error handling and recovery

### Rollback Procedures
1. **API Rollback**: Revert to Phase 1 stable state
2. **Database Rollback**: Rollback any schema changes
3. **Frontend Rollback**: Revert context integrations
4. **Cache Rollback**: Clear all cached aggregations

## Phase Completion Checklist

### Pre-Completion Validation
- [ ] All 5 components (2.1-2.5) completed
- [ ] All acceptance criteria met
- [ ] Integration tests passing
- [ ] Performance benchmarks met
- [ ] Security audit completed

### Phase Sign-off
- [ ] **Technical Lead Approval**: Code review completed
- [ ] **Business Logic Review**: Calculations verified
- [ ] **Integration Test**: Frontend integration working
- [ ] **Performance Test**: Meets performance requirements
- [ ] **Security Review**: Security checklist verified

### Next Phase Preparation
- [ ] Phase 3 dependencies verified
- [ ] Advanced feature requirements reviewed
- [ ] Team briefed on Phase 3 objectives

---

**Phase 2 Success Criteria:** Core business logic APIs operational with real-time capabilities  
**Next Phase:** Phase 3 - Advanced Feature APIs  
**Estimated Start Date:** 2024-02-21
