import React from 'react'
import { Outlet } from 'react-router-dom'
import { Navbar } from './Navbar'
import { Sidebar } from './Sidebar'
import { useSidebar } from '@/context/GlobalContext'

export function MainLayout() {
  const { sidebarOpen } = useSidebar()

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Top Navigation */}
      <Navbar />
      
      <div className="flex pt-16">
        {/* Sidebar */}
        <Sidebar />
        
        {/* Main Content */}
        <div 
          className={`flex-1 transition-all duration-300 ${
            sidebarOpen ? 'ml-64' : 'ml-0'
          }`}
        >
          <main className="p-6">
            <Outlet />
          </main>
        </div>
      </div>
    </div>
  )
}
