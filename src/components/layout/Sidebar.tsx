import React from 'react'
import { NavLink } from 'react-router-dom'
import { cn } from '@/lib/utils'
import { useSidebar } from '@/context/GlobalContext'

const navigationItems = [
  { name: 'Dashboard', path: '/', icon: 'fas fa-tachometer-alt' },
  { name: 'Expense Tracking', path: '/expenses', icon: 'fas fa-receipt' },
  { name: 'Profitability Analysis', path: '/profitability', icon: 'fas fa-chart-line' },
  { name: 'Input Management', path: '/inputs', icon: 'fas fa-seedling' },
  { name: 'Equipment Management', path: '/equipment', icon: 'fas fa-tractor' },
  { name: 'Real-time Financials', path: '/financials', icon: 'fas fa-chart-area' },
  { name: 'Grain Marketing', path: '/grain-marketing', icon: 'fas fa-wheat' },
  { name: 'Crop Planning', path: '/crop-planning', icon: 'fas fa-map' },
  { name: 'Working Capital', path: '/working-capital', icon: 'fas fa-piggy-bank' },
  { name: 'Reports & Analytics', path: '/reports', icon: 'fas fa-chart-bar' },
  { name: 'Settings', path: '/settings', icon: 'fas fa-cog' },
]

export function Sidebar() {
  const { isOpen } = useSidebar()

  return (
    <aside
      className={cn(
        "fixed left-0 top-16 h-[calc(100vh-4rem)] bg-gray-50 border-r border-gray-200 transition-all duration-300 z-40",
        isOpen ? "w-64" : "w-0 overflow-hidden"
      )}
    >
      <div className="p-4">
        <nav className="space-y-2">
          {navigationItems.map((item) => (
            <NavLink
              key={item.path}
              to={item.path}
              className={({ isActive }) =>
                cn(
                  "flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors",
                  isActive
                    ? "bg-green-100 text-green-700"
                    : "text-gray-600 hover:bg-gray-100 hover:text-gray-900"
                )
              }
            >
              <i className={cn(item.icon, "w-5 h-5")} />
              <span className="whitespace-nowrap">{item.name}</span>
            </NavLink>
          ))}
        </nav>
      </div>
    </aside>
  )
}
