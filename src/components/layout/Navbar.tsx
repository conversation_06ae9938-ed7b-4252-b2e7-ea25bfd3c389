import React from 'react'
import { Button } from '@/components/ui/button'
import { useSidebar } from '@/context/GlobalContext'

export function Navbar() {
  const { toggleSidebar } = useSidebar()

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-green-600 text-white shadow-lg">
      <div className="flex items-center justify-between px-6 py-4">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleSidebar}
            className="text-white hover:bg-green-700 rounded-button whitespace-nowrap cursor-pointer"
          >
            <i className="fas fa-bars"></i>
          </Button>
          <h1 className="text-xl font-bold">Harvest Profit Pro</h1>
        </div>
        
        <div className="flex items-center space-x-6">
          <nav className="hidden md:flex space-x-6">
            <Button 
              variant="ghost" 
              className="text-white hover:bg-green-700 rounded-button whitespace-nowrap cursor-pointer"
            >
              Dashboard
            </Button>
            <Button 
              variant="ghost" 
              className="text-white hover:bg-green-700 rounded-button whitespace-nowrap cursor-pointer"
            >
              Reports
            </Button>
            <Button 
              variant="ghost" 
              className="text-white hover:bg-green-700 rounded-button whitespace-nowrap cursor-pointer"
            >
              Analytics
            </Button>
          </nav>
          
          <div className="flex items-center space-x-4">
            <Button 
              variant="ghost" 
              size="sm" 
              className="text-white hover:bg-green-700 rounded-button whitespace-nowrap cursor-pointer"
            >
              <i className="fas fa-bell"></i>
            </Button>
            <Button 
              variant="ghost" 
              size="sm" 
              className="text-white hover:bg-green-700 rounded-button whitespace-nowrap cursor-pointer"
            >
              <i className="fas fa-user-circle"></i>
            </Button>
          </div>
        </div>
      </div>
    </nav>
  )
}
