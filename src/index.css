@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Harvest Profit Pro Design System Variables */
    
    /* Colors */
    --primary-green: #4CAF50;
    --secondary-green: #8BC34A;
    --accent-yellow: #FFC107;
    --success: #4CAF50;
    --error: #F44336;
    --warning: #FF9800;
    --info: #2196F3;
    
    /* Text Colors */
    --text-primary: #333333;
    --text-secondary: #666666;
    --text-light: #999999;
    --text-white: #ffffff;
    
    /* Background Colors */
    --bg-primary: #F5F5F5;
    --bg-card: #ffffff;
    --bg-sidebar: #ffffff;
    --bg-navigation: #4CAF50;
    --bg-item: #F9F9F9;
    --bg-hover: #F0F0F0;
    
    /* Layout Dimensions */
    --sidebar-width: 250px;
    --navbar-height: 64px;
    --card-border-radius: 0.5rem;
    --button-border-radius: 0.375rem;
    
    /* Spacing Scale */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-12: 3rem;
    --space-16: 4rem;
    
    /* Typography Scale */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    --text-6xl: 3.75rem;
    
    /* Font Weights */
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* shadcn/ui CSS variables */
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 142 76% 36%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 142 76% 36%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 142 76% 36%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 142 76% 36%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
  }
}

/* Custom utility classes for Harvest Profit Pro */
@layer utilities {
  .rounded-button {
    border-radius: var(--button-border-radius);
  }
  
  .transition-shadow {
    transition: box-shadow var(--transition-normal);
  }
  
  .transition-transform {
    transition: transform var(--transition-normal);
  }
  
  .sidebar-transition {
    transition: transform var(--transition-normal);
  }
}

/* Custom scrollbar styles */
.scroll-area::-webkit-scrollbar {
  width: 6px;
}

.scroll-area::-webkit-scrollbar-track {
  background: transparent;
}

.scroll-area::-webkit-scrollbar-thumb {
  background-color: var(--text-light);
  border-radius: 3px;
}

.scroll-area::-webkit-scrollbar-thumb:hover {
  background-color: var(--text-secondary);
}
