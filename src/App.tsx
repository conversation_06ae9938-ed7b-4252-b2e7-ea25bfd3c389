import React from 'react'
import { Routes, Route } from 'react-router-dom'
import { MainLayout } from '@/components/layout/MainLayout'
import { Dashboard } from '@/features/dashboard/components/Dashboard'
import { ExpenseManagement } from '@/features/expense-tracking/components/ExpenseManagement'
import { ProfitabilityAnalysis } from '@/features/profitability-analysis/components/ProfitabilityAnalysis'
import { InputManagement } from '@/features/input-management/components/InputManagement'
import { EquipmentManagement } from '@/features/equipment-management/components/EquipmentManagement'
import { FinancialOverview } from '@/features/real-time-financials/components/FinancialOverview'
import { GrainMarketing } from '@/features/grain-marketing/components/GrainMarketing'
import { CropPlanning } from '@/features/crop-planning/components/CropPlanning'
import { WorkingCapital } from '@/features/working-capital/components/WorkingCapital'
import { ReportsAnalytics } from '@/features/reports-analytics/components/ReportsAnalytics'
import { Settings } from '@/features/settings/components/Settings'
import { AuthProvider } from '@/features/auth/context/AuthContext'
import { GlobalProvider } from '@/context/GlobalContext'
import { Toaster } from '@/components/ui/toaster'

function App() {
  return (
    <AuthProvider>
      <GlobalProvider>
        <div className="min-h-screen bg-gray-50">
          <Routes>
            <Route path="/" element={<MainLayout />}>
              <Route index element={<Dashboard />} />
              <Route path="dashboard" element={<Dashboard />} />
              <Route path="expenses" element={<ExpenseManagement />} />
              <Route path="profitability" element={<ProfitabilityAnalysis />} />
              <Route path="inputs" element={<InputManagement />} />
              <Route path="equipment" element={<EquipmentManagement />} />
              <Route path="financials" element={<FinancialOverview />} />
              <Route path="grain-marketing" element={<GrainMarketing />} />
              <Route path="crop-planning" element={<CropPlanning />} />
              <Route path="capital" element={<WorkingCapital />} />
              <Route path="reports" element={<ReportsAnalytics />} />
              <Route path="settings" element={<Settings />} />
            </Route>
          </Routes>
          <Toaster />
        </div>
      </GlobalProvider>
    </AuthProvider>
  )
}

export default App
