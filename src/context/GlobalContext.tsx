import React, { createContext, useContext, useReducer, ReactNode } from 'react'

// Global state interface
interface GlobalState {
  user: User | null
  isLoading: boolean
  error: string | null
  notifications: Notification[]
  theme: 'light' | 'dark'
  sidebarOpen: boolean
  currentFarm: Farm | null
  farms: Farm[]
}

// Types
interface User {
  id: string
  email: string
  name: string
  role: 'admin' | 'manager' | 'user'
  farmIds: string[]
}

interface Farm {
  id: string
  name: string
  location: string
  totalAcres: number
  ownerId: string
}

interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message: string
  timestamp: Date
  read: boolean
}

// Action types
type GlobalAction =
  | { type: 'SET_USER'; payload: User | null }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'ADD_NOTIFICATION'; payload: Omit<Notification, 'id' | 'timestamp' | 'read'> }
  | { type: 'REMOVE_NOTIFICATION'; payload: string }
  | { type: 'MARK_NOTIFICATION_READ'; payload: string }
  | { type: 'TOGGLE_THEME' }
  | { type: 'TOGGLE_SIDEBAR' }
  | { type: 'SET_SIDEBAR_OPEN'; payload: boolean }
  | { type: 'SET_CURRENT_FARM'; payload: Farm | null }
  | { type: 'SET_FARMS'; payload: Farm[] }

// Initial state
const initialState: GlobalState = {
  user: null,
  isLoading: false,
  error: null,
  notifications: [],
  theme: 'light',
  sidebarOpen: true,
  currentFarm: null,
  farms: [],
}

// Reducer
function globalReducer(state: GlobalState, action: GlobalAction): GlobalState {
  switch (action.type) {
    case 'SET_USER':
      return { ...state, user: action.payload }
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload }
    case 'SET_ERROR':
      return { ...state, error: action.payload }
    case 'ADD_NOTIFICATION':
      return {
        ...state,
        notifications: [
          {
            ...action.payload,
            id: Date.now().toString(),
            timestamp: new Date(),
            read: false,
          },
          ...state.notifications,
        ],
      }
    case 'REMOVE_NOTIFICATION':
      return {
        ...state,
        notifications: state.notifications.filter(n => n.id !== action.payload),
      }
    case 'MARK_NOTIFICATION_READ':
      return {
        ...state,
        notifications: state.notifications.map(n =>
          n.id === action.payload ? { ...n, read: true } : n
        ),
      }
    case 'TOGGLE_THEME':
      return { ...state, theme: state.theme === 'light' ? 'dark' : 'light' }
    case 'TOGGLE_SIDEBAR':
      return { ...state, sidebarOpen: !state.sidebarOpen }
    case 'SET_SIDEBAR_OPEN':
      return { ...state, sidebarOpen: action.payload }
    case 'SET_CURRENT_FARM':
      return { ...state, currentFarm: action.payload }
    case 'SET_FARMS':
      return { ...state, farms: action.payload }
    default:
      return state
  }
}

// Context
const GlobalContext = createContext<{
  state: GlobalState
  dispatch: React.Dispatch<GlobalAction>
} | null>(null)

// Provider
export function GlobalProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(globalReducer, initialState)

  return (
    <GlobalContext.Provider value={{ state, dispatch }}>
      {children}
    </GlobalContext.Provider>
  )
}

// Hook
export function useGlobal() {
  const context = useContext(GlobalContext)
  if (!context) {
    throw new Error('useGlobal must be used within a GlobalProvider')
  }
  return context
}

// Convenience hooks
export function useUser() {
  const { state } = useGlobal()
  return state.user
}

export function useNotifications() {
  const { state, dispatch } = useGlobal()
  
  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => {
    dispatch({ type: 'ADD_NOTIFICATION', payload: notification })
  }
  
  const removeNotification = (id: string) => {
    dispatch({ type: 'REMOVE_NOTIFICATION', payload: id })
  }
  
  const markAsRead = (id: string) => {
    dispatch({ type: 'MARK_NOTIFICATION_READ', payload: id })
  }
  
  return {
    notifications: state.notifications,
    addNotification,
    removeNotification,
    markAsRead,
  }
}

export function useSidebar() {
  const { state, dispatch } = useGlobal()
  
  const toggleSidebar = () => {
    dispatch({ type: 'TOGGLE_SIDEBAR' })
  }
  
  const setSidebarOpen = (open: boolean) => {
    dispatch({ type: 'SET_SIDEBAR_OPEN', payload: open })
  }
  
  return {
    sidebarOpen: state.sidebarOpen,
    toggleSidebar,
    setSidebarOpen,
  }
}

export function useFarm() {
  const { state, dispatch } = useGlobal()
  
  const setCurrentFarm = (farm: Farm | null) => {
    dispatch({ type: 'SET_CURRENT_FARM', payload: farm })
  }
  
  const setFarms = (farms: Farm[]) => {
    dispatch({ type: 'SET_FARMS', payload: farms })
  }
  
  return {
    currentFarm: state.currentFarm,
    farms: state.farms,
    setCurrentFarm,
    setFarms,
  }
}

// Export types for use in other files
export type { User, Farm, Notification, GlobalState, GlobalAction }
