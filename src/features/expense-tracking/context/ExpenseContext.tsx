import React, { createContext, useContext, useReducer, ReactNode, useEffect } from 'react'
import { Expense, ExpenseCategory, ExpenseFilters, ExpenseSummary } from '../types'

// Expense state interface
interface ExpenseState {
  expenses: Expense[]
  categories: ExpenseCategory[]
  summary: ExpenseSummary | null
  filters: ExpenseFilters
  isLoading: boolean
  error: string | null
  selectedExpense: Expense | null
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// Action types
type ExpenseAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_EXPENSES'; payload: { expenses: Expense[]; pagination: any } }
  | { type: 'SET_CATEGORIES'; payload: ExpenseCategory[] }
  | { type: 'SET_SUMMARY'; payload: ExpenseSummary }
  | { type: 'SET_FILTERS'; payload: Partial<ExpenseFilters> }
  | { type: 'SET_SELECTED_EXPENSE'; payload: Expense | null }
  | { type: 'ADD_EXPENSE'; payload: Expense }
  | { type: 'UPDATE_EXPENSE'; payload: Expense }
  | { type: 'DELETE_EXPENSE'; payload: string }

// Initial state
const initialState: ExpenseState = {
  expenses: [],
  categories: [],
  summary: null,
  filters: {
    dateRange: {
      start: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
      end: new Date(),
    },
    categories: [],
    fields: [],
    amountRange: { min: 0, max: 10000 },
    searchTerm: '',
  },
  isLoading: false,
  error: null,
  selectedExpense: null,
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
  },
}

// Reducer
function expenseReducer(state: ExpenseState, action: ExpenseAction): ExpenseState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload }
    case 'SET_ERROR':
      return { ...state, error: action.payload, isLoading: false }
    case 'SET_EXPENSES':
      return { 
        ...state, 
        expenses: action.payload.expenses, 
        pagination: action.payload.pagination,
        error: null 
      }
    case 'SET_CATEGORIES':
      return { ...state, categories: action.payload, error: null }
    case 'SET_SUMMARY':
      return { ...state, summary: action.payload, error: null }
    case 'SET_FILTERS':
      return { ...state, filters: { ...state.filters, ...action.payload } }
    case 'SET_SELECTED_EXPENSE':
      return { ...state, selectedExpense: action.payload }
    case 'ADD_EXPENSE':
      return { 
        ...state, 
        expenses: [action.payload, ...state.expenses],
        pagination: { ...state.pagination, total: state.pagination.total + 1 }
      }
    case 'UPDATE_EXPENSE':
      return {
        ...state,
        expenses: state.expenses.map(expense =>
          expense.id === action.payload.id ? action.payload : expense
        ),
        selectedExpense: state.selectedExpense?.id === action.payload.id ? action.payload : state.selectedExpense
      }
    case 'DELETE_EXPENSE':
      return {
        ...state,
        expenses: state.expenses.filter(expense => expense.id !== action.payload),
        selectedExpense: state.selectedExpense?.id === action.payload ? null : state.selectedExpense,
        pagination: { ...state.pagination, total: state.pagination.total - 1 }
      }
    default:
      return state
  }
}

// Context
const ExpenseContext = createContext<{
  state: ExpenseState
  dispatch: React.Dispatch<ExpenseAction>
  loadExpenses: () => Promise<void>
  loadCategories: () => Promise<void>
  loadSummary: () => Promise<void>
  createExpense: (expense: any) => Promise<void>
  updateExpense: (expense: any) => Promise<void>
  deleteExpense: (id: string) => Promise<void>
} | null>(null)

// Provider
export function ExpenseProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(expenseReducer, initialState)

  const loadExpenses = async () => {
    dispatch({ type: 'SET_LOADING', payload: true })
    try {
      // TODO: Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Mock data
      const mockExpenses: Expense[] = [
        {
          id: '1',
          date: '2024-01-15',
          category: { id: '1', name: 'Inputs', color: '#10B981', icon: 'fas fa-seedling' },
          amount: 2450,
          description: 'Fertilizer Purchase - Field A',
          notes: 'Nitrogen fertilizer for spring planting',
          fieldId: 'field-1',
          createdAt: '2024-01-15T10:00:00Z',
          updatedAt: '2024-01-15T10:00:00Z',
        },
        {
          id: '2',
          date: '2024-01-14',
          category: { id: '2', name: 'Equipment', color: '#F59E0B', icon: 'fas fa-tractor' },
          amount: 380,
          description: 'Fuel for Tractor Maintenance',
          createdAt: '2024-01-14T14:30:00Z',
          updatedAt: '2024-01-14T14:30:00Z',
        },
      ]

      const mockPagination = {
        page: 1,
        limit: 20,
        total: mockExpenses.length,
        totalPages: 1,
      }

      dispatch({ type: 'SET_EXPENSES', payload: { expenses: mockExpenses, pagination: mockPagination } })
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Unknown error' })
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false })
    }
  }

  const loadCategories = async () => {
    try {
      // Mock categories
      const mockCategories: ExpenseCategory[] = [
        { id: '1', name: 'Inputs', color: '#10B981', icon: 'fas fa-seedling' },
        { id: '2', name: 'Equipment', color: '#F59E0B', icon: 'fas fa-tractor' },
        { id: '3', name: 'Labor', color: '#3B82F6', icon: 'fas fa-users' },
        { id: '4', name: 'Utilities', color: '#8B5CF6', icon: 'fas fa-bolt' },
        { id: '5', name: 'Insurance', color: '#EF4444', icon: 'fas fa-shield-alt' },
      ]

      dispatch({ type: 'SET_CATEGORIES', payload: mockCategories })
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Unknown error' })
    }
  }

  const loadSummary = async () => {
    try {
      // Mock summary
      const mockSummary: ExpenseSummary = {
        totalExpenses: 85320,
        expensesByCategory: [
          { categoryId: '1', categoryName: 'Inputs', amount: 35000, percentage: 41 },
          { categoryId: '2', categoryName: 'Equipment', amount: 25000, percentage: 29 },
          { categoryId: '3', categoryName: 'Labor', amount: 15000, percentage: 18 },
          { categoryId: '4', categoryName: 'Utilities', amount: 7000, percentage: 8 },
          { categoryId: '5', categoryName: 'Insurance', amount: 3320, percentage: 4 },
        ],
        expensesByMonth: [],
        averageExpensePerDay: 280,
        topExpenses: [],
      }

      dispatch({ type: 'SET_SUMMARY', payload: mockSummary })
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Unknown error' })
    }
  }

  const createExpense = async (expenseData: any) => {
    try {
      // TODO: API call
      const newExpense: Expense = {
        id: Date.now().toString(),
        ...expenseData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }
      dispatch({ type: 'ADD_EXPENSE', payload: newExpense })
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Unknown error' })
    }
  }

  const updateExpense = async (expenseData: any) => {
    try {
      // TODO: API call
      const updatedExpense: Expense = {
        ...expenseData,
        updatedAt: new Date().toISOString(),
      }
      dispatch({ type: 'UPDATE_EXPENSE', payload: updatedExpense })
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Unknown error' })
    }
  }

  const deleteExpense = async (id: string) => {
    try {
      // TODO: API call
      dispatch({ type: 'DELETE_EXPENSE', payload: id })
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Unknown error' })
    }
  }

  useEffect(() => {
    loadExpenses()
    loadCategories()
    loadSummary()
  }, [])

  return (
    <ExpenseContext.Provider value={{ 
      state, 
      dispatch, 
      loadExpenses, 
      loadCategories, 
      loadSummary,
      createExpense,
      updateExpense,
      deleteExpense
    }}>
      {children}
    </ExpenseContext.Provider>
  )
}

// Hook
export function useExpenseContext() {
  const context = useContext(ExpenseContext)
  if (!context) {
    throw new Error('useExpenseContext must be used within an ExpenseProvider')
  }
  return context
}

// Export types
export type { ExpenseState, ExpenseAction }
