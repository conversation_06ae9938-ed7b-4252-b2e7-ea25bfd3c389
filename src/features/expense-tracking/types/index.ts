// Expense Tracking feature types

export interface Expense {
  id: string
  date: string
  category: ExpenseCategory
  subcategory?: string
  amount: number
  description: string
  notes?: string
  fieldId?: string
  receiptUrl?: string
  tags?: string[]
  createdAt: string
  updatedAt: string
}

export interface ExpenseCategory {
  id: string
  name: string
  color: string
  icon: string
  subcategories?: ExpenseSubcategory[]
}

export interface ExpenseSubcategory {
  id: string
  name: string
  categoryId: string
}

export interface ExpenseFilters {
  dateRange: {
    start: Date
    end: Date
  }
  categories: string[]
  fields: string[]
  amountRange: {
    min: number
    max: number
  }
  searchTerm: string
}

export interface ExpenseSummary {
  totalExpenses: number
  expensesByCategory: {
    categoryId: string
    categoryName: string
    amount: number
    percentage: number
  }[]
  expensesByMonth: {
    month: string
    amount: number
  }[]
  averageExpensePerDay: number
  topExpenses: Expense[]
}

export interface CreateExpenseRequest {
  date: string
  categoryId: string
  subcategoryId?: string
  amount: number
  description: string
  notes?: string
  fieldId?: string
  tags?: string[]
}

export interface UpdateExpenseRequest extends Partial<CreateExpenseRequest> {
  id: string
}

// API response types
export interface ExpensesApiResponse {
  expenses: Expense[]
  total: number
  page: number
  limit: number
  totalPages: number
}

export interface ExpenseCategoriesApiResponse {
  categories: ExpenseCategory[]
}
