import React from 'react'
import { MetricCard } from '../atoms/MetricCard'
import { useDashboard } from '../../hooks/useDashboard'

export function MetricsGrid() {
  const { metrics } = useDashboard()

  if (!metrics) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-gray-200 animate-pulse rounded-lg h-32"></div>
        ))}
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      <MetricCard
        title="Total Revenue"
        value={metrics.totalRevenue}
        change={metrics.revenueChange}
        format="currency"
      />
      <MetricCard
        title="Total Expenses"
        value={metrics.totalExpenses}
        change={metrics.expensesChange}
        format="currency"
      />
      <MetricCard
        title="Net Profit"
        value={metrics.netProfit}
        change={metrics.profitChange}
        format="currency"
      />
      <MetricCard
        title="Profit per Acre"
        value={metrics.profitPerAcre}
        change={metrics.profitPerAcreChange}
        format="currency"
      />
    </div>
  )
}
