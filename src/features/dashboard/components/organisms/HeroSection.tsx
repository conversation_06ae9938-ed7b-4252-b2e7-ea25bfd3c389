import React from 'react'
import { useDashboard } from '../../hooks/useDashboard'
import { formatDate } from '@/lib/utils'

export function HeroSection() {
  const { lastUpdated } = useDashboard()

  return (
    <div className="bg-gradient-to-r from-green-600 to-green-700 text-white rounded-lg p-6 mb-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold mb-2">Farm Dashboard</h1>
          <p className="text-green-100 text-lg">
            Welcome back! Here's your farm's financial overview.
          </p>
        </div>
        <div className="mt-4 md:mt-0 text-right">
          <p className="text-green-100 text-sm">
            Last updated: {lastUpdated ? formatDate(lastUpdated) : 'Never'}
          </p>
          <div className="flex items-center justify-end mt-2 space-x-2">
            <div className="w-2 h-2 bg-green-300 rounded-full animate-pulse"></div>
            <span className="text-green-100 text-sm">Live data</span>
          </div>
        </div>
      </div>
    </div>
  )
}
