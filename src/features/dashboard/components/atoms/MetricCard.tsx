import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { formatCurrency, formatPercentage } from '@/lib/utils'
import { cn } from '@/lib/utils'

interface MetricCardProps {
  title: string
  value: number
  change: number
  format?: 'currency' | 'number' | 'percentage'
  className?: string
}

export function MetricCard({ 
  title, 
  value, 
  change, 
  format = 'currency',
  className 
}: MetricCardProps) {
  const formatValue = (val: number) => {
    switch (format) {
      case 'currency':
        return formatCurrency(val)
      case 'percentage':
        return formatPercentage(val)
      default:
        return val.toLocaleString()
    }
  }

  const isPositive = change >= 0
  const changeColor = isPositive ? 'text-green-500' : 'text-red-500'

  return (
    <Card className={cn(
      "bg-white shadow-lg hover:shadow-xl transition-shadow duration-300",
      className
    )}>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium text-gray-600">
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold text-gray-800">
          {formatValue(value)}
        </div>
        <p className="text-xs text-gray-500 mt-1">
          <span className={changeColor}>
            {isPositive ? '+' : ''}{formatPercentage(change)}
          </span>
          {' '}from last month
        </p>
      </CardContent>
    </Card>
  )
}
