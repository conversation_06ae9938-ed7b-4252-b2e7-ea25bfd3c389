import React from 'react'
import { HeroSection } from './organisms/HeroSection'
import { MetricsGrid } from './organisms/MetricsGrid'
import { ChartsSection } from './organisms/ChartsSection'
import { RecentActivities } from './organisms/RecentActivities'
import { useDashboard } from '../hooks/useDashboard'

export function Dashboard() {
  const { isLoading, error } = useDashboard()

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-lg text-gray-600">Loading dashboard...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-lg text-red-600">Error loading dashboard: {error}</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <HeroSection />
      <MetricsGrid />
      <ChartsSection />
      <RecentActivities />
    </div>
  )
}
