// Dashboard feature types

export interface DashboardMetrics {
  totalRevenue: number
  totalExpenses: number
  netProfit: number
  profitPerAcre: number
  revenueChange: number
  expensesChange: number
  profitChange: number
  profitPerAcreChange: number
}

export interface RecentExpense {
  id: string
  date: string
  description: string
  amount: number
  category: string
}

export interface FieldPerformance {
  id: string
  field: string
  acres: number
  revenue: number
  expenses: number
  profit: number
  status: 'Excellent' | 'Good' | 'Fair' | 'Poor'
}

export interface ChartData {
  labels: string[]
  datasets: {
    label: string
    data: number[]
    backgroundColor?: string | string[]
    borderColor?: string | string[]
    borderWidth?: number
  }[]
}

export interface DashboardFilters {
  dateRange: {
    start: Date
    end: Date
  }
  fields: string[]
  categories: string[]
}

// API response types
export interface DashboardApiResponse {
  metrics: DashboardMetrics
  recentExpenses: RecentExpense[]
  fieldPerformance: FieldPerformance[]
  chartData: {
    revenue: ChartData
    expenses: ChartData
    profit: ChartData
  }
}
