import { useDashboardContext } from '../context/DashboardContext'
import { useCallback } from 'react'

export function useDashboard() {
  const { state, refreshData } = useDashboardContext()

  const refresh = useCallback(async () => {
    await refreshData()
  }, [refreshData])

  return {
    metrics: state.metrics,
    recentExpenses: state.recentExpenses,
    fieldPerformance: state.fieldPerformance,
    isLoading: state.isLoading,
    error: state.error,
    lastUpdated: state.lastUpdated,
    refresh,
  }
}
