import React, { createContext, useContext, useReducer, ReactNode, useEffect } from 'react'
import { DashboardMetrics, RecentExpense, FieldPerformance } from '../types'

// Dashboard state interface
interface DashboardState {
  metrics: DashboardMetrics | null
  recentExpenses: RecentExpense[]
  fieldPerformance: FieldPerformance[]
  isLoading: boolean
  error: string | null
  lastUpdated: Date | null
}

// Action types
type DashboardAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_METRICS'; payload: DashboardMetrics }
  | { type: 'SET_RECENT_EXPENSES'; payload: RecentExpense[] }
  | { type: 'SET_FIELD_PERFORMANCE'; payload: FieldPerformance[] }
  | { type: 'UPDATE_LAST_UPDATED' }

// Initial state
const initialState: DashboardState = {
  metrics: null,
  recentExpenses: [],
  fieldPerformance: [],
  isLoading: false,
  error: null,
  lastUpdated: null,
}

// Reducer
function dashboardReducer(state: DashboardState, action: DashboardAction): DashboardState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload }
    case 'SET_ERROR':
      return { ...state, error: action.payload, isLoading: false }
    case 'SET_METRICS':
      return { ...state, metrics: action.payload, error: null }
    case 'SET_RECENT_EXPENSES':
      return { ...state, recentExpenses: action.payload, error: null }
    case 'SET_FIELD_PERFORMANCE':
      return { ...state, fieldPerformance: action.payload, error: null }
    case 'UPDATE_LAST_UPDATED':
      return { ...state, lastUpdated: new Date() }
    default:
      return state
  }
}

// Context
const DashboardContext = createContext<{
  state: DashboardState
  dispatch: React.Dispatch<DashboardAction>
  refreshData: () => Promise<void>
} | null>(null)

// Provider
export function DashboardProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(dashboardReducer, initialState)

  const refreshData = async () => {
    dispatch({ type: 'SET_LOADING', payload: true })
    try {
      // TODO: Replace with actual API calls
      // Simulate API calls for now
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Mock data - replace with actual API calls
      const mockMetrics: DashboardMetrics = {
        totalRevenue: 128450,
        totalExpenses: 85320,
        netProfit: 43130,
        profitPerAcre: 432,
        revenueChange: 12.5,
        expensesChange: 8.2,
        profitChange: 18.7,
        profitPerAcreChange: 15.3,
      }

      const mockRecentExpenses: RecentExpense[] = [
        {
          id: '1',
          date: '2024-01-15',
          description: 'Fertilizer Purchase - Field A',
          amount: 2450,
          category: 'Inputs',
        },
        {
          id: '2',
          date: '2024-01-14',
          description: 'Fuel for Tractor Maintenance',
          amount: 380,
          category: 'Equipment',
        },
      ]

      const mockFieldPerformance: FieldPerformance[] = [
        {
          id: '1',
          field: 'North Field',
          acres: 120,
          revenue: 45000,
          expenses: 28000,
          profit: 17000,
          status: 'Excellent',
        },
        {
          id: '2',
          field: 'South Field',
          acres: 85,
          revenue: 32000,
          expenses: 25000,
          profit: 7000,
          status: 'Good',
        },
      ]

      dispatch({ type: 'SET_METRICS', payload: mockMetrics })
      dispatch({ type: 'SET_RECENT_EXPENSES', payload: mockRecentExpenses })
      dispatch({ type: 'SET_FIELD_PERFORMANCE', payload: mockFieldPerformance })
      dispatch({ type: 'UPDATE_LAST_UPDATED' })
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error instanceof Error ? error.message : 'Unknown error' })
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false })
    }
  }

  useEffect(() => {
    refreshData()
  }, [])

  return (
    <DashboardContext.Provider value={{ state, dispatch, refreshData }}>
      {children}
    </DashboardContext.Provider>
  )
}

// Hook
export function useDashboardContext() {
  const context = useContext(DashboardContext)
  if (!context) {
    throw new Error('useDashboardContext must be used within a DashboardProvider')
  }
  return context
}

// Export types
export type { DashboardState, DashboardAction }
