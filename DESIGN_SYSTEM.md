# Harvest Profit Pro - Design System

## Overview
This design system maintains visual consistency across all features of the Harvest Profit Pro application, extracted from the existing Dashboard implementation.

## Color Palette

### Primary Colors
```css
--primary-green: #4CAF50;      /* Main brand color, buttons, navigation */
--secondary-green: #8BC34A;    /* Secondary elements, chart colors */
--accent-yellow: #FFC107;      /* Accent color, call-to-action elements */
```

### Semantic Colors
```css
--success: #4CAF50;            /* Profit indicators, positive values */
--error: #F44336;              /* Loss indicators, negative values, errors */
--warning: #FF9800;            /* Warning states, alerts */
--info: #2196F3;               /* Information, neutral states */
```

### Background Colors
```css
--bg-primary: #F5F5F5;         /* Main application background */
--bg-card: #FFFFFF;            /* Card backgrounds */
--bg-sidebar: #FFFFFF;         /* Sidebar background */
--bg-navigation: #4CAF50;      /* Top navigation background */
--bg-item: #F9FAFB;            /* List item, table row backgrounds */
--bg-hover: #F3F4F6;           /* Hover states */
```

### Text Colors
```css
--text-primary: #333333;       /* Main text, headings */
--text-secondary: #6B7280;     /* Secondary text, descriptions */
--text-light: #9CA3AF;         /* Light text, placeholders */
--text-white: #FFFFFF;         /* White text for dark backgrounds */
```

### Chart Colors
```css
--chart-primary: #4CAF50;      /* Primary chart color */
--chart-secondary: #8BC34A;    /* Secondary chart color */
--chart-accent: #FFC107;       /* Accent chart color */
--chart-neutral: #9E9E9E;      /* Neutral chart color */
--chart-orange: #FF9800;       /* Orange chart color */
--chart-blue: #607D8B;         /* Blue chart color */
--chart-brown: #795548;        /* Brown chart color */
--chart-purple: #9C27B0;       /* Purple chart color */
```

## Typography

### Font Stack
```css
font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
```

### Type Scale & Usage Patterns (Extracted from Dashboard)
```css
--text-xs: 0.75rem;    /* 12px - Badge text, percentage labels, small captions */
--text-sm: 0.875rem;   /* 14px - Card labels, secondary text, form inputs, field descriptions */
--text-base: 1rem;     /* 16px - Default body text, form labels */
--text-lg: 1.125rem;   /* 18px - Card titles, section headers, chart titles */
--text-xl: 1.25rem;    /* 20px - Navigation brand, hero subtitles */
--text-2xl: 1.5rem;    /* 24px - Page titles, main section headers */
--text-3xl: 1.875rem;  /* 30px - Large metric displays, cost analysis */
--text-4xl: 2.25rem;   /* 36px - Hero headings, main dashboard title */
--text-6xl: 3.75rem;   /* 60px - Large icons, construction placeholders */
```

### Font Weights & Semantic Usage
```css
--font-normal: 400;    /* Regular body text, descriptions, secondary information */
--font-medium: 500;    /* Card labels, field labels, emphasized descriptions */
--font-semibold: 600;  /* Card titles, section headers, important values */
--font-bold: 700;      /* Page titles, hero headings, main navigation, metric values */
```

### Typography Hierarchy Patterns
```css
/* Hero Section */
.hero-title {
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  color: var(--text-white);
  margin-bottom: 0.5rem;
}

.hero-subtitle {
  font-size: var(--text-xl);
  font-weight: var(--font-normal);
  color: var(--text-white);
  opacity: 0.9;
}

/* Page Headers */
.page-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
}

/* Card Titles */
.card-title-large {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.card-title-small {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
}

/* Metric Values */
.metric-large {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--success); /* or --error for negative values */
}

.metric-extra-large {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--success);
}

/* Body Text */
.body-text {
  font-size: var(--text-sm);
  font-weight: var(--font-normal);
  color: var(--text-primary);
}

.body-text-secondary {
  font-size: var(--text-sm);
  font-weight: var(--font-normal);
  color: var(--text-secondary);
}

/* Labels and Captions */
.label {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

.caption {
  font-size: var(--text-xs);
  font-weight: var(--font-normal);
  color: var(--text-light);
}

/* Navigation */
.nav-brand {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--text-white);
}

.nav-item {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-white);
}
```

## Layout System & Spacing Patterns

### Spacing Scale (Extracted from Dashboard Usage)
```css
--space-1: 0.25rem;    /* 4px - Minimal spacing */
--space-2: 0.5rem;     /* 8px - Small spacing, card header padding bottom */
--space-3: 0.75rem;    /* 12px - Medium spacing, icon margins */
--space-4: 1rem;       /* 16px - Standard spacing, card content padding, sidebar nav padding */
--space-5: 1.25rem;    /* 20px - Large spacing */
--space-6: 1.5rem;     /* 24px - Section spacing, main content padding */
--space-8: 2rem;       /* 32px - Large section spacing */
--space-12: 3rem;      /* 48px - Extra large spacing, coming soon padding */
--space-16: 4rem;      /* 64px - Navigation height, top padding */
```

### Grid System Patterns (From Dashboard)
```css
/* Main Layout Grids */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 1.5rem; /* gap-6 */
}

@media (min-width: 768px) {
  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .metrics-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Charts Grid */
.charts-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 1.5rem; /* gap-6 */
}

@media (min-width: 1024px) {
  .charts-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Expense Categories Grid */
.categories-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 1rem; /* gap-4 */
}

@media (min-width: 768px) {
  .categories-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .categories-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Field Performance Grid */
.field-performance-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem; /* gap-4 */
}
```

### Flexbox Patterns
```css
/* Navigation Flex Patterns */
.nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem; /* px-6 py-4 */
}

.nav-section {
  display: flex;
  align-items: center;
  gap: 1rem; /* space-x-4 */
}

.nav-links {
  display: flex;
  gap: 1.5rem; /* space-x-6 */
}

/* Card Content Flex */
.card-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem; /* p-3 */
}

.expense-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem; /* p-4 */
}

.expense-details {
  display: flex;
  align-items: center;
  gap: 1rem; /* space-x-4 */
}
```

### Layout Dimensions & Structure
```css
--sidebar-width: 250px;        /* Fixed sidebar width */
--navbar-height: 64px;         /* Top navigation height (pt-16) */
--card-border-radius: 0.5rem;  /* 8px - Card border radius */
--button-border-radius: 0.375rem; /* 6px - Button border radius */
--hero-height: 16rem;          /* 256px - Hero section height */
--scroll-area-height: 16rem;   /* 256px - Recent expenses scroll area */
```

### Responsive Breakpoints (Tailwind-based)
```css
--breakpoint-sm: 640px;    /* Small devices - stack layouts */
--breakpoint-md: 768px;    /* Medium devices - 2-column grids */
--breakpoint-lg: 1024px;   /* Large devices - 3-4 column grids */
--breakpoint-xl: 1280px;   /* Extra large devices - full layouts */
```

### Spacing Patterns by Component Type
```css
/* Page Level Spacing */
.page-container {
  padding: 1.5rem; /* p-6 */
}

.section-spacing {
  margin-bottom: 1.5rem; /* space-y-6 */
}

/* Card Spacing */
.card-header {
  padding: 1.5rem 1.5rem 0.5rem 1.5rem; /* pb-2 */
}

.card-content {
  padding: 0 1.5rem 1.5rem 1.5rem;
}

.card-content-spaced {
  padding: 0 1.5rem 1.5rem 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem; /* space-y-4 */
}

/* List Item Spacing */
.list-item {
  padding: 0.75rem; /* p-3 */
  margin-bottom: 1rem; /* space-y-4 */
}

.list-item-large {
  padding: 1rem; /* p-4 */
  margin-bottom: 0.75rem; /* space-y-3 */
}

/* Form Spacing */
.form-container {
  display: flex;
  flex-direction: column;
  gap: 1rem; /* space-y-4 */
}

/* Navigation Spacing */
.nav-item-spacing {
  margin-bottom: 0.5rem; /* space-y-2 */
}

.nav-icon-spacing {
  margin-right: 0.75rem; /* mr-3 */
}
```

## Component Specifications & UI Patterns

### Button Patterns (Extracted from Dashboard)
```css
/* Primary Action Button */
.btn-primary {
  background-color: var(--primary-green); /* #4CAF50 */
  color: var(--text-white);
  border-radius: var(--button-border-radius);
  padding: 0.5rem 1rem;
  font-weight: var(--font-medium);
  transition: all 0.2s ease;
  white-space: nowrap;
  cursor: pointer;
}

.btn-primary:hover {
  background-color: #45a049;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Accent Button (Call-to-Action) */
.btn-accent {
  background-color: var(--accent-yellow); /* #FFC107 */
  color: var(--text-primary); /* #333333 */
  border-radius: var(--button-border-radius);
  padding: 0.5rem 1rem;
  font-weight: var(--font-medium);
  transition: all 0.2s ease;
  white-space: nowrap;
  cursor: pointer;
}

/* Ghost Button (Navigation) */
.btn-ghost {
  background-color: transparent;
  color: var(--text-white);
  border-radius: var(--button-border-radius);
  padding: 0.5rem 1rem;
  font-weight: var(--font-medium);
  transition: all 0.2s ease;
  white-space: nowrap;
  cursor: pointer;
}

.btn-ghost:hover {
  background-color: rgba(255, 255, 255, 0.1); /* hover:bg-green-700 equivalent */
}

/* Outline Button (Form Selectors) */
.btn-outline {
  background-color: transparent;
  color: var(--text-primary);
  border: 1px solid var(--text-light);
  border-radius: var(--button-border-radius);
  padding: 0.5rem 1rem;
  font-weight: var(--font-medium);
  transition: all 0.2s ease;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  white-space: nowrap;
  cursor: pointer;
}

/* Icon Button */
.btn-icon {
  background-color: transparent;
  color: var(--text-white);
  border-radius: var(--button-border-radius);
  padding: 0.5rem;
  font-weight: var(--font-medium);
  transition: all 0.2s ease;
  cursor: pointer;
}

/* Sidebar Navigation Button */
.btn-nav {
  background-color: transparent;
  color: var(--text-secondary);
  border-radius: var(--button-border-radius);
  padding: 0.5rem 1rem;
  font-weight: var(--font-medium);
  transition: all 0.2s ease;
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  white-space: nowrap;
  cursor: pointer;
}

.btn-nav:hover {
  background-color: var(--bg-hover);
}

.btn-nav.active {
  background-color: var(--primary-green);
  color: var(--text-white);
}
```

### Card Patterns
```css
/* Standard Card */
.card {
  background-color: var(--bg-card);
  border-radius: var(--card-border-radius);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.3s ease;
}

.card:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Metric Card (KPI Cards) */
.card-metric {
  background-color: var(--bg-card);
  border-radius: var(--card-border-radius);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.3s ease;
}

.card-metric:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.card-metric-header {
  padding: 1.5rem 1.5rem 0.5rem 1.5rem;
}

.card-metric-content {
  padding: 0 1.5rem 1.5rem 1.5rem;
}

.card-metric-title {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
}

.card-metric-value {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--success); /* or --error for negative values */
}

.card-metric-change {
  font-size: var(--text-xs);
  color: var(--text-light);
  margin-top: 0.25rem;
}

/* Chart Card */
.card-chart {
  background-color: var(--bg-card);
  border-radius: var(--card-border-radius);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.card-chart-header {
  padding: 1.5rem 1.5rem 1rem 1.5rem;
}

.card-chart-content {
  padding: 0 1.5rem 1.5rem 1.5rem;
}

.card-chart-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

/* List Item Card */
.card-list-item {
  background-color: var(--bg-item);
  border-radius: var(--card-border-radius);
  padding: 0.75rem; /* p-3 for small items */
  margin-bottom: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-list-item-large {
  background-color: var(--bg-item);
  border-radius: var(--card-border-radius);
  padding: 1rem; /* p-4 for larger items */
  margin-bottom: 0.75rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
```

### Form Elements
```css
/* Form Label */
.form-label {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  display: block;
}

/* Form Input */
.form-input {
  border: 1px solid var(--text-light);
  border-radius: var(--card-border-radius);
  padding: 0.5rem 0.75rem;
  font-size: var(--text-sm);
  color: var(--text-primary);
  background-color: var(--bg-card);
  transition: border-color 0.2s ease;
  width: 100%;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-green);
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.form-input::placeholder {
  color: var(--text-light);
}
```

### Badge Patterns
```css
/* Secondary Badge (Category Labels) */
.badge-secondary {
  background-color: var(--bg-hover);
  color: var(--text-primary);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  display: inline-block;
}

/* Status Badge (Excellent/Good) */
.badge-success {
  background-color: var(--success);
  color: var(--text-white);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  display: inline-block;
}

.badge-warning {
  background-color: var(--warning);
  color: var(--text-white);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  display: inline-block;
}
```

### Progress Bar
```css
.progress-bar {
  width: 100%;
  height: 0.5rem; /* h-2 */
  background-color: var(--bg-hover);
  border-radius: 0.25rem;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  background-color: var(--primary-green);
  transition: width 0.3s ease;
}

.progress-bar-small {
  width: 5rem; /* w-20 */
  height: 0.5rem; /* h-2 */
  background-color: var(--bg-hover);
  border-radius: 0.25rem;
  overflow: hidden;
}
```

### Navigation Patterns
```css
/* Top Navigation */
.navbar {
  background-color: var(--bg-navigation);
  color: var(--text-white);
  height: var(--navbar-height);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
}

.navbar-brand {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  color: var(--text-white);
}

.navbar-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.navbar-links {
  display: none;
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .navbar-links {
    display: flex;
  }
}

/* Sidebar */
.sidebar {
  background-color: var(--bg-sidebar);
  width: var(--sidebar-width);
  position: fixed;
  left: 0;
  top: var(--navbar-height);
  height: calc(100vh - var(--navbar-height));
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  z-index: 40;
}

.sidebar.collapsed {
  transform: translateX(-100%);
}

.sidebar-content {
  height: 100%;
  overflow-y: auto;
  padding: 1rem;
}

.sidebar-nav {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}
```

### Scroll Area
```css
.scroll-area {
  height: 16rem; /* h-64 */
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--text-light) transparent;
}

.scroll-area::-webkit-scrollbar {
  width: 6px;
}

.scroll-area::-webkit-scrollbar-track {
  background: transparent;
}

.scroll-area::-webkit-scrollbar-thumb {
  background-color: var(--text-light);
  border-radius: 3px;
}

.scroll-area::-webkit-scrollbar-thumb:hover {
  background-color: var(--text-secondary);
}
```

### Icon System & Patterns
```css
/* Icon Library: FontAwesome 6 */
.icon {
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
}

/* Icon Sizes */
.icon-sm { font-size: 0.875rem; }    /* 14px */
.icon-base { font-size: 1rem; }      /* 16px */
.icon-lg { font-size: 1.25rem; }     /* 20px */
.icon-xl { font-size: 1.5rem; }      /* 24px */
.icon-6xl { font-size: 3.75rem; }    /* 60px - for placeholders */

/* Icon Colors */
.icon-primary { color: var(--text-primary); }
.icon-secondary { color: var(--text-secondary); }
.icon-light { color: var(--text-light); }
.icon-white { color: var(--text-white); }
.icon-success { color: var(--success); }
.icon-error { color: var(--error); }

/* Icon Spacing */
.icon-mr-2 { margin-right: 0.5rem; }
.icon-mr-3 { margin-right: 0.75rem; }
.icon-mb-4 { margin-bottom: 1rem; }

/* Icon Containers */
.icon-container {
  width: 2.5rem;  /* w-10 */
  height: 2.5rem; /* h-10 */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-container-red {
  background-color: rgba(244, 67, 54, 0.1); /* bg-red-100 */
}
```

## Animation & Transitions
```css
--transition-fast: 0.15s ease;     /* Quick interactions */
--transition-normal: 0.3s ease;    /* Standard transitions */
--transition-slow: 0.5s ease;      /* Slow, emphasized transitions */

/* Hover Transitions */
.transition-shadow {
  transition: box-shadow var(--transition-normal);
}

.transition-transform {
  transition: transform var(--transition-normal);
}

.transition-all {
  transition: all var(--transition-fast);
}

/* Sidebar Animation */
.sidebar-transition {
  transition: transform var(--transition-normal);
}
```

## Usage Guidelines & Implementation

### Color Usage Principles
1. **Primary Green (#4CAF50)**: Use for main actions, navigation, profit indicators, and primary buttons
2. **Secondary Green (#8BC34A)**: Use for secondary actions, chart accents, and supporting elements
3. **Accent Yellow (#FFC107)**: Use sparingly for call-to-action elements and hero buttons
4. **Semantic Colors**: Always use success (green) for profits, error (red) for losses/expenses
5. **Background Hierarchy**: White for cards, light gray for main background, darker gray for item backgrounds

### Typography Hierarchy
1. **Hero Headings (text-4xl, font-bold)**: Main dashboard welcome, primary page titles
2. **Page Titles (text-2xl, font-bold)**: Section headers like "Expense Management"
3. **Card Titles (text-lg, font-semibold)**: Chart titles, section headers within cards
4. **Metric Labels (text-sm, font-medium)**: KPI card labels, form labels
5. **Body Text (text-sm, font-normal)**: Descriptions, secondary information
6. **Captions (text-xs, font-normal)**: Small labels, percentages, metadata

### Layout Patterns
1. **Grid Systems**: Use responsive grids (1→2→4 columns for metrics, 1→2 for charts)
2. **Card Spacing**: Maintain consistent gap-6 (1.5rem) between cards
3. **Content Spacing**: Use space-y-6 for main sections, space-y-4 for card content
4. **Navigation**: Fixed 64px top nav, 250px collapsible sidebar
5. **Responsive**: Stack layouts on mobile, expand to full grids on desktop

### Component Usage Guidelines

#### Buttons
- **Primary**: Main actions (Add Expense, Save, Submit)
- **Accent**: Hero call-to-actions (Get Started)
- **Ghost**: Navigation items, icon buttons
- **Outline**: Form selectors, dropdown triggers

#### Cards
- **Metric Cards**: KPI displays with hover effects
- **Chart Cards**: Data visualization containers
- **List Cards**: Scrollable content areas
- **Form Cards**: Input grouping and organization

#### Icons
- **FontAwesome 6**: Consistent icon library across all features
- **Semantic Usage**: Receipt for expenses, tractor for equipment, etc.
- **Spacing**: mr-2 for inline icons, mr-3 for navigation icons

### Implementation Notes

#### CSS Custom Properties
All design tokens are defined as CSS custom properties for easy theming and consistency:
```css
:root {
  /* Colors */
  --primary-green: #4CAF50;
  --secondary-green: #8BC34A;
  --accent-yellow: #FFC107;

  /* Spacing */
  --space-6: 1.5rem;
  --sidebar-width: 250px;
  --navbar-height: 64px;

  /* Typography */
  --text-2xl: 1.5rem;
  --font-bold: 700;
}
```

#### Tailwind CSS Integration
The design system is built on Tailwind CSS classes for rapid development:
- Use utility classes for spacing, colors, and typography
- Extend Tailwind config for custom colors and spacing
- Maintain consistency with existing class patterns

#### Component Library Structure
Organize components following the extracted patterns:
```
components/
  ui/
    Button.tsx        // All button variants
    Card.tsx          // Card components with headers/content
    Badge.tsx         // Status and category badges
    Progress.tsx      // Progress bars and indicators
    Input.tsx         // Form inputs with consistent styling
    Label.tsx         // Form labels
  layout/
    Navbar.tsx        // Top navigation
    Sidebar.tsx       // Collapsible sidebar
    MainLayout.tsx    // Overall layout structure
```

### Accessibility Considerations
1. **Color Contrast**: All text meets WCAG AA standards
2. **Focus States**: Visible focus indicators on all interactive elements
3. **Semantic HTML**: Proper heading hierarchy and landmark elements
4. **Icon Labels**: Descriptive text for screen readers
5. **Keyboard Navigation**: Full keyboard accessibility for all features

### Performance Optimizations
1. **Icon Loading**: Use FontAwesome CDN or tree-shaking for used icons only
2. **Image Optimization**: Optimize hero background images
3. **CSS Optimization**: Use CSS custom properties for theme consistency
4. **Component Lazy Loading**: Load feature components on demand

This comprehensive design system ensures visual consistency, accessibility, and maintainability across all features of Harvest Profit Pro while preserving the professional agricultural aesthetic established in the original Dashboard.
